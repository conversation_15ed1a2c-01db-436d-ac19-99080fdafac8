/**
 * WebSocket Connection Manager - <PERSON>ton Pattern
 * Ensures only ONE WebSocket connection to Dhan API per application instance
 * Manages multi-instrument subscriptions and connection lifecycle
 */

import { DhanWebSocketMarketDataService, DhanWebSocketConfig } from './websocket-market-data';
import { ProcessedMarketData } from '@/types/dhan';

type WebSocketEventHandler = (data: ProcessedMarketData) => void;
type ConnectionStatusHandler = (status: 'connected' | 'connecting' | 'disconnected' | 'error') => void;

interface SubscriptionRequest {
  instruments: string[];
  handler: WebSocketEventHandler;
  id: string;
}

interface WebSocketManagerStatus {
  isConnected: boolean;
  isConnecting: boolean;
  isAuthenticated: boolean;
  reconnectAttempts: number;
  subscribedInstruments: string[];
  activeSubscriptions: number;
  connectionId: string | null;
}

export class WebSocketManager {
  private static instance: WebSocketManager | null = null;
  private wsService: DhanWebSocketMarketDataService | null = null;
  private subscriptions: Map<string, SubscriptionRequest> = new Map();
  private dataHandlers: Map<string, WebSocketEventHandler> = new Map();
  private statusHandlers: Set<ConnectionStatusHandler> = new Set();
  private config: DhanWebSocketConfig | null = null;
  private connectionId: string | null = null;
  private isInitializing: boolean = false;
  private initializationPromise: Promise<boolean> | null = null;

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get singleton instance
   */
  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Initialize WebSocket connection with configuration
   */
  async initialize(config: DhanWebSocketConfig): Promise<boolean> {
    // Prevent multiple simultaneous initialization attempts
    if (this.isInitializing && this.initializationPromise) {
      console.log('WebSocket initialization already in progress, waiting...');
      return await this.initializationPromise;
    }

    // If already connected with same config, return success
    if (this.wsService && this.config && this.areConfigsEqual(this.config, config)) {
      const status = this.wsService.getStatus();
      if (status.isConnected) {
        console.log('WebSocket already connected with same configuration');
        return true;
      }
    }

    this.isInitializing = true;
    this.initializationPromise = this.performInitialization(config);

    try {
      const result = await this.initializationPromise;
      return result;
    } finally {
      this.isInitializing = false;
      this.initializationPromise = null;
    }
  }

  /**
   * Perform the actual initialization
   */
  private async performInitialization(config: DhanWebSocketConfig): Promise<boolean> {
    try {
      console.log('Initializing WebSocket connection...');

      // Disconnect existing connection if different config
      if (this.wsService && (!this.areConfigsEqual(this.config!, config))) {
        console.log('Configuration changed, disconnecting existing connection');
        this.disconnect();
      }

      // Create new WebSocket service if needed
      if (!this.wsService) {
        this.wsService = new DhanWebSocketMarketDataService(config);
        this.config = config;
        this.connectionId = this.generateConnectionId();

        // Set up event handlers
        this.setupEventHandlers();
      }

      // Attempt connection
      const connected = await this.wsService.connect();
      
      if (connected) {
        console.log(`WebSocket connected successfully (ID: ${this.connectionId})`);
        this.notifyStatusHandlers('connected');
        
        // Re-subscribe to all active subscriptions
        await this.resubscribeAll();
        
        return true;
      } else {
        console.error('Failed to establish WebSocket connection');
        this.notifyStatusHandlers('error');
        return false;
      }

    } catch (error) {
      console.error('WebSocket initialization error:', error);
      this.notifyStatusHandlers('error');
      return false;
    }
  }

  /**
   * Subscribe to instruments with a unique handler
   */
  subscribe(instruments: string[], handler: WebSocketEventHandler, subscriptionId?: string): string {
    const id = subscriptionId || this.generateSubscriptionId();
    
    // Store subscription request
    const subscription: SubscriptionRequest = {
      instruments,
      handler,
      id
    };
    
    this.subscriptions.set(id, subscription);
    this.dataHandlers.set(id, handler);

    // If WebSocket is connected, subscribe immediately
    if (this.wsService && this.wsService.getStatus().isConnected) {
      this.wsService.subscribe(instruments);
      console.log(`Subscribed to instruments: ${instruments.join(', ')} (ID: ${id})`);
    } else {
      console.log(`Subscription queued for when connection is established (ID: ${id})`);
    }

    return id;
  }

  /**
   * Unsubscribe from instruments
   */
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      console.warn(`Subscription not found: ${subscriptionId}`);
      return false;
    }

    // Remove from local tracking
    this.subscriptions.delete(subscriptionId);
    this.dataHandlers.delete(subscriptionId);

    // If WebSocket is connected, unsubscribe from server
    if (this.wsService && this.wsService.getStatus().isConnected) {
      // Note: Dhan WebSocket doesn't have explicit unsubscribe, 
      // so we just remove from local tracking
      console.log(`Unsubscribed from subscription: ${subscriptionId}`);
    }

    return true;
  }

  /**
   * Add connection status handler
   */
  onStatusChange(handler: ConnectionStatusHandler): () => void {
    this.statusHandlers.add(handler);
    
    // Return cleanup function
    return () => {
      this.statusHandlers.delete(handler);
    };
  }

  /**
   * Get current connection status
   */
  getStatus(): WebSocketManagerStatus {
    const wsStatus = this.wsService?.getStatus();
    
    return {
      isConnected: wsStatus?.isConnected || false,
      isConnecting: wsStatus?.isConnecting || false,
      isAuthenticated: wsStatus?.isAuthenticated || false,
      reconnectAttempts: wsStatus?.reconnectAttempts || 0,
      subscribedInstruments: wsStatus?.subscribedInstruments || [],
      activeSubscriptions: this.subscriptions.size,
      connectionId: this.connectionId
    };
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.wsService) {
      console.log(`Disconnecting WebSocket (ID: ${this.connectionId})`);
      this.wsService.disconnect();
      this.wsService = null;
    }
    
    this.config = null;
    this.connectionId = null;
    this.subscriptions.clear();
    this.dataHandlers.clear();
    this.notifyStatusHandlers('disconnected');
  }

  /**
   * Force reconnection
   */
  async reconnect(): Promise<boolean> {
    if (!this.config) {
      console.error('Cannot reconnect: No configuration available');
      return false;
    }

    console.log('Force reconnecting WebSocket...');
    this.disconnect();
    return await this.initialize(this.config);
  }

  // Private helper methods

  private setupEventHandlers(): void {
    if (!this.wsService) return;

    this.wsService.onData((data: ProcessedMarketData) => {
      // Broadcast to all active handlers
      this.dataHandlers.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error('Error in data handler:', error);
        }
      });
    });

    this.wsService.onConnected(() => {
      console.log('WebSocket connected event received');
      this.notifyStatusHandlers('connected');
    });

    this.wsService.onDisconnected(() => {
      console.log('WebSocket disconnected event received');
      this.notifyStatusHandlers('disconnected');
    });

    this.wsService.onError((error) => {
      console.error('WebSocket error event received:', error);
      this.notifyStatusHandlers('error');
    });
  }

  private async resubscribeAll(): Promise<void> {
    if (!this.wsService || this.subscriptions.size === 0) return;

    console.log(`Re-subscribing to ${this.subscriptions.size} active subscriptions`);
    
    // Collect all unique instruments
    const allInstruments = new Set<string>();
    this.subscriptions.forEach(sub => {
      sub.instruments.forEach(instrument => allInstruments.add(instrument));
    });

    // Subscribe to all instruments at once
    if (allInstruments.size > 0) {
      this.wsService.subscribe(Array.from(allInstruments));
    }
  }

  private notifyStatusHandlers(status: 'connected' | 'connecting' | 'disconnected' | 'error'): void {
    this.statusHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        console.error('Error in status handler:', error);
      }
    });
  }

  private areConfigsEqual(config1: DhanWebSocketConfig, config2: DhanWebSocketConfig): boolean {
    return config1.accessToken === config2.accessToken && 
           config1.clientId === config2.clientId &&
           config1.version === config2.version;
  }

  private generateConnectionId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const webSocketManager = WebSocketManager.getInstance();
