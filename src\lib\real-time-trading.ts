/**
 * Real-time Trading Service
 * Connects WebSocket market data to EMA strategy and triggers trades
 */

import { ProcessedMarketData } from '@/types/dhan';
import { strategyService, TradingSignal } from './strategies/strategy-service';
import { tradingConfigService } from './trading-config';
import { tradingLogger } from './trading-logger';

interface ExecutedTrade {
  tradeId: string;
  orderId: string;
  status: string;
  tradingMode: 'SANDBOX' | 'LIVE';
  isSuccessful: boolean;
  message: string;
}

export interface TradingServiceConfig {
  userId: string;
  onSignalGenerated?: (signal: TradingSignal) => void;
  onTradeExecuted?: (trade: ExecutedTrade) => void;
  onError?: (error: Error) => void;
}

export class RealTimeTradingService {
  private userId: string;
  private isActive: boolean = false;
  private onSignalGenerated?: (signal: TradingSignal) => void;
  private onTradeExecuted?: (trade: ExecutedTrade) => void;
  private onError?: (error: Error) => void;
  private lastProcessedTime: Map<string, number> = new Map();
  private readonly PROCESSING_INTERVAL = 5000; // 5 seconds minimum between processing same instrument

  constructor(config: TradingServiceConfig) {
    this.userId = config.userId;
    this.onSignalGenerated = config.onSignalGenerated;
    this.onTradeExecuted = config.onTradeExecuted;
    this.onError = config.onError;
  }

  /**
   * Start real-time trading service
   */
  start(): void {
    this.isActive = true;
    console.log(`Real-time trading service started for user: ${this.userId}`);
  }

  /**
   * Stop real-time trading service
   */
  stop(): void {
    this.isActive = false;
    console.log(`Real-time trading service stopped for user: ${this.userId}`);
  }

  /**
   * Process incoming market data
   */
  async processMarketData(marketData: ProcessedMarketData): Promise<void> {
    if (!this.isActive) {
      return;
    }

    try {
      // Check if enough time has passed since last processing for this instrument
      const lastProcessed = this.lastProcessedTime.get(marketData.symbol) || 0;
      const now = Date.now();
      
      if (now - lastProcessed < this.PROCESSING_INTERVAL) {
        return; // Skip processing to avoid too frequent signals
      }

      // Update last processed time
      this.lastProcessedTime.set(marketData.symbol, now);

      // Process market data through strategy
      const signals = await strategyService.processMarketData(marketData, this.userId);

      // Handle generated signals
      for (const signal of signals) {
        await this.handleSignal(signal);
      }

    } catch (error) {
      console.error('Error processing market data:', error);
      this.onError?.(error instanceof Error ? error : new Error('Unknown error'));
    }
  }

  /**
   * Handle generated trading signal
   */
  private async handleSignal(signal: TradingSignal): Promise<void> {
    try {
      // Notify listeners about signal generation
      this.onSignalGenerated?.(signal);

      // Check if auto-trade is enabled
      if (!signal.autoTradeEnabled) {
        console.log(`Signal generated but auto-trade disabled: ${signal.signalType} ${signal.instrumentSymbol} at ${signal.signalPrice}`);
        return;
      }

      // Check trading limits
      const canTrade = await tradingConfigService.canPlaceTrade(this.userId);
      if (!canTrade) {
        console.log(`Signal generated but trading limits reached: ${signal.signalType} ${signal.instrumentSymbol}`);
        return;
      }

      // Get enabled instruments
      const enabledInstruments = await tradingConfigService.getEnabledInstruments(this.userId);
      const isInstrumentEnabled = enabledInstruments.some(
        inst => inst.symbol === signal.instrumentSymbol
      );

      if (!isInstrumentEnabled) {
        console.log(`Signal generated but instrument not enabled: ${signal.instrumentSymbol}`);
        return;
      }

      // Execute trade
      await this.executeTrade(signal);

    } catch (error) {
      console.error('Error handling signal:', error);
      tradingLogger.logSystem(this.userId, `Error handling signal: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
      this.onError?.(error instanceof Error ? error : new Error('Unknown error'));
    }
  }

  /**
   * Execute trade based on signal
   */
  private async executeTrade(signal: TradingSignal): Promise<void> {
    try {
      // Generate correlation ID for idempotency
      const correlationId = `${signal.userId}_${signal.instrumentSymbol}_${signal.signalType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Get instrument details
      const enabledInstruments = await tradingConfigService.getEnabledInstruments(this.userId);
      const instrument = enabledInstruments.find(inst => inst.symbol === signal.instrumentSymbol);

      if (!instrument) {
        throw new Error(`Instrument not found: ${signal.instrumentSymbol}`);
      }

      // Prepare order request
      const orderRequest = {
        userId: signal.userId,
        signalId: signal.id,
        orderType: signal.signalType,
        instrumentSymbol: signal.instrumentSymbol,
        instrumentName: signal.instrumentName,
        instrumentSecurityId: instrument.securityId.toString(),
        instrumentExchange: instrument.exchange,
        price: signal.signalPrice,
        quantity: signal.strategyParams.defaultQuantity || 1,
        correlationId,
        tradingMode: signal.tradingMode
      };

      // Log trade attempt
      tradingLogger.logTradeAttempt(signal.userId, orderRequest, correlationId);

      // Call order placement API
      const response = await fetch('/api/trading/place-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderRequest),
      });

      const result = await response.json();

      if (result.success) {
        console.log(`Trade executed successfully: ${signal.signalType} ${signal.instrumentSymbol} - Order ID: ${result.data.orderId}`);
        tradingLogger.logTradeSuccess(signal.userId, result.data, correlationId);
        this.onTradeExecuted?.(result.data);
      } else {
        console.error(`Trade execution failed: ${result.error}`);
        tradingLogger.logTradeFailure(signal.userId, result.error, orderRequest, correlationId);
        throw new Error(result.error);
      }

    } catch (error) {
      console.error('Error executing trade:', error);
      this.onError?.(error instanceof Error ? error : new Error('Unknown error'));
    }
  }

  /**
   * Get service status
   */
  getStatus(): {
    isActive: boolean;
    userId: string;
    lastProcessedTimes: Record<string, number>;
  } {
    return {
      isActive: this.isActive,
      userId: this.userId,
      lastProcessedTimes: Object.fromEntries(this.lastProcessedTime)
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<TradingServiceConfig>): void {
    if (config.onSignalGenerated !== undefined) {
      this.onSignalGenerated = config.onSignalGenerated;
    }
    if (config.onTradeExecuted !== undefined) {
      this.onTradeExecuted = config.onTradeExecuted;
    }
    if (config.onError !== undefined) {
      this.onError = config.onError;
    }
  }
}

/**
 * Trading Service Manager
 * Manages multiple trading services for different users
 */
export class TradingServiceManager {
  private static instance: TradingServiceManager;
  private services: Map<string, RealTimeTradingService> = new Map();

  private constructor() {}

  static getInstance(): TradingServiceManager {
    if (!TradingServiceManager.instance) {
      TradingServiceManager.instance = new TradingServiceManager();
    }
    return TradingServiceManager.instance;
  }

  /**
   * Create or get trading service for user
   */
  getService(userId: string, config?: Omit<TradingServiceConfig, 'userId'>): RealTimeTradingService {
    if (!this.services.has(userId)) {
      this.services.set(userId, new RealTimeTradingService({
        userId,
        ...config
      }));
    }

    const service = this.services.get(userId)!;
    
    // Update config if provided
    if (config) {
      service.updateConfig(config);
    }

    return service;
  }

  /**
   * Remove trading service for user
   */
  removeService(userId: string): void {
    const service = this.services.get(userId);
    if (service) {
      service.stop();
      this.services.delete(userId);
    }
  }

  /**
   * Process market data for all active services
   */
  async processMarketDataForAll(marketData: ProcessedMarketData): Promise<void> {
    const promises = Array.from(this.services.values()).map(service => 
      service.processMarketData(marketData)
    );

    await Promise.allSettled(promises);
  }

  /**
   * Get all active services
   */
  getActiveServices(): { userId: string; status: unknown }[] {
    return Array.from(this.services.entries()).map(([userId, service]) => ({
      userId,
      status: service.getStatus()
    }));
  }

  /**
   * Stop all services
   */
  stopAll(): void {
    this.services.forEach(service => service.stop());
    this.services.clear();
  }
}

// Export singleton instance
export const tradingServiceManager = TradingServiceManager.getInstance();
