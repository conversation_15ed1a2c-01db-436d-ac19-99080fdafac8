/**
 * Profile Data Cache Service
 * Implements single fetch strategy with intelligent caching to prevent rate limiting
 */

import { ProcessedProfileData } from '@/types/dhan';

interface CachedProfileData {
  data: ProcessedProfileData;
  timestamp: number;
  expiresAt: number;
}

interface ProfileCacheConfig {
  cacheKey: string;
  defaultExpiryMinutes: number;
  minRefreshIntervalMinutes: number;
  maxRetryAttempts: number;
  retryDelayMs: number;
}

const DEFAULT_CONFIG: ProfileCacheConfig = {
  cacheKey: 'dhan_profile_cache',
  defaultExpiryMinutes: 30, // Cache for 30 minutes by default
  minRefreshIntervalMinutes: 15, // Minimum 15 minutes between API calls
  maxRetryAttempts: 3,
  retryDelayMs: 60000 // 1 minute delay between retries
};

export class ProfileCacheService {
  private config: ProfileCacheConfig;
  private lastFetchAttempt: number = 0;
  private retryCount: number = 0;
  private isRefreshing: boolean = false;

  constructor(config: Partial<ProfileCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Get cached profile data or fetch if needed
   */
  async getProfileData(forceRefresh: boolean = false): Promise<ProcessedProfileData | null> {
    try {
      // Check if we have valid cached data and don't need to refresh
      if (!forceRefresh) {
        const cachedData = this.getCachedData();
        if (cachedData && this.isDataValid(cachedData)) {
          // Using cached profile data
          return cachedData.data;
        }
      }

      // Check if we can make a new API call (respect rate limits)
      if (!this.canMakeApiCall() && !forceRefresh) {
        // Rate limit protection: using cached data
        const cachedData = this.getCachedData();
        return cachedData?.data || null;
      }

      // Prevent multiple simultaneous refresh attempts
      if (this.isRefreshing) {
        // Profile refresh already in progress, using cached data
        const cachedData = this.getCachedData();
        return cachedData?.data || null;
      }

      // Fetch fresh data from API
      return await this.fetchAndCacheProfileData();

    } catch (error) {
      console.error('Error in getProfileData:', error);
      
      // Return cached data as fallback
      const cachedData = this.getCachedData();
      return cachedData?.data || null;
    }
  }

  /**
   * Fetch profile data from API and cache it
   */
  private async fetchAndCacheProfileData(): Promise<ProcessedProfileData | null> {
    this.isRefreshing = true;
    this.lastFetchAttempt = Date.now();

    try {
      // Fetching fresh profile data from API
      
      const response = await fetch('/api/dhan/profile');
      
      if (!response.ok) {
        if (response.status === 429) {
          console.warn('Rate limit exceeded for profile API');
          this.handleRateLimit();
          throw new Error('Rate limit exceeded. Please wait before making more requests.');
        }
        throw new Error(`Profile API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to fetch profile data');
      }

      // Cache the successful response
      this.cacheProfileData(result.data);
      this.retryCount = 0; // Reset retry count on success
      
      // Profile data fetched and cached successfully
      return result.data;

    } catch (error) {
      console.error('Failed to fetch profile data:', error);
      this.retryCount++;
      
      // Return cached data as fallback
      const cachedData = this.getCachedData();
      return cachedData?.data || null;
      
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Cache profile data with expiration
   */
  private cacheProfileData(data: ProcessedProfileData): void {
    const now = Date.now();
    const expiresAt = now + (this.config.defaultExpiryMinutes * 60 * 1000);
    
    const cacheData: CachedProfileData = {
      data,
      timestamp: now,
      expiresAt
    };

    try {
      localStorage.setItem(this.config.cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to cache profile data in localStorage:', error);
      // Fallback to session storage
      try {
        sessionStorage.setItem(this.config.cacheKey, JSON.stringify(cacheData));
      } catch (sessionError) {
        console.warn('Failed to cache profile data in sessionStorage:', sessionError);
      }
    }
  }

  /**
   * Get cached profile data from storage
   */
  private getCachedData(): CachedProfileData | null {
    try {
      // Try localStorage first
      let cached = localStorage.getItem(this.config.cacheKey);
      
      // Fallback to sessionStorage
      if (!cached) {
        cached = sessionStorage.getItem(this.config.cacheKey);
      }

      if (!cached) {
        return null;
      }

      return JSON.parse(cached) as CachedProfileData;
    } catch (error) {
      console.warn('Failed to parse cached profile data:', error);
      return null;
    }
  }

  /**
   * Check if cached data is still valid
   */
  private isDataValid(cachedData: CachedProfileData): boolean {
    const now = Date.now();
    return now < cachedData.expiresAt;
  }

  /**
   * Check if we can make an API call (rate limit protection)
   */
  private canMakeApiCall(): boolean {
    const now = Date.now();
    const timeSinceLastAttempt = now - this.lastFetchAttempt;
    const minInterval = this.config.minRefreshIntervalMinutes * 60 * 1000;
    
    return timeSinceLastAttempt >= minInterval;
  }

  /**
   * Handle rate limit response
   */
  private handleRateLimit(): void {
    // Extend the minimum refresh interval when rate limited
    this.lastFetchAttempt = Date.now() + (5 * 60 * 1000); // Add 5 minutes penalty
  }

  /**
   * Clear cached data
   */
  clearCache(): void {
    try {
      localStorage.removeItem(this.config.cacheKey);
      sessionStorage.removeItem(this.config.cacheKey);
      // Profile cache cleared
    } catch (error) {
      console.warn('Failed to clear profile cache:', error);
    }
  }

  /**
   * Get cache status information
   */
  getCacheStatus(): {
    hasCachedData: boolean;
    isValid: boolean;
    lastFetch: Date | null;
    expiresAt: Date | null;
    canRefresh: boolean;
  } {
    const cachedData = this.getCachedData();
    const canRefresh = this.canMakeApiCall();

    return {
      hasCachedData: !!cachedData,
      isValid: cachedData ? this.isDataValid(cachedData) : false,
      lastFetch: cachedData ? new Date(cachedData.timestamp) : null,
      expiresAt: cachedData ? new Date(cachedData.expiresAt) : null,
      canRefresh
    };
  }

  /**
   * Force refresh profile data (ignores rate limiting)
   */
  async forceRefresh(): Promise<ProcessedProfileData | null> {
    // Force refreshing profile data
    return await this.getProfileData(true);
  }
}

// Export singleton instance
export const profileCache = new ProfileCacheService();

// Export for testing with custom config
// ProfileCacheService is already exported at class declaration
