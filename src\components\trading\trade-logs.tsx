/**
 * Trade Logs Component
 * Displays executed trades with detailed information
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  ExternalLink,
  RefreshCw
} from 'lucide-react';

interface TradeLog {
  id: string;
  order_type: 'BUY' | 'SELL';
  instrument_symbol: string;
  instrument_name: string;
  order_price: number;
  quantity: number;
  order_status: 'PENDING' | 'PLACED' | 'EXECUTED' | 'CANCELLED' | 'REJECTED' | 'FAILED';
  dhan_order_id?: string;
  execution_price?: number;
  execution_quantity?: number;
  execution_time?: string;
  trading_mode: 'SANDBOX' | 'LIVE';
  error_message?: string;
  created_at: string;
  correlation_id: string;
}

interface TradeLogsProps {
  trades: TradeLog[];
  loading?: boolean;
  onRefresh?: () => void;
}

export function TradeLogs({ trades, loading = false, onRefresh }: TradeLogsProps) {
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short'
    });
  };

  const formatPrice = (price: number) => {
    return `₹${price.toFixed(2)}`;
  };

  const getTradeIcon = (type: 'BUY' | 'SELL') => {
    return type === 'BUY' ? (
      <TrendingUp className="h-4 w-4 text-green-400" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-400" />
    );
  };

  const getStatusBadge = (status: string, errorMessage?: string) => {
    switch (status) {
      case 'EXECUTED':
        return (
          <Badge variant="default" className="text-xs bg-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            Executed
          </Badge>
        );
      case 'PLACED':
      case 'PENDING':
        return (
          <Badge variant="secondary" className="text-xs">
            <Clock className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        );
      case 'CANCELLED':
        return (
          <Badge variant="outline" className="text-xs">
            <XCircle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        );
      case 'REJECTED':
      case 'FAILED':
        return (
          <Badge variant="destructive" className="text-xs" title={errorMessage}>
            <AlertTriangle className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-xs">
            {status}
          </Badge>
        );
    }
  };

  const calculatePnL = (trade: TradeLog) => {
    if (!trade.execution_price || !trade.execution_quantity) return null;
    
    // Simple P&L calculation (this would be more complex in real trading)
    const executedValue = trade.execution_price * trade.execution_quantity;
    const orderValue = trade.order_price * trade.quantity;
    const pnl = trade.order_type === 'BUY' ? 0 : executedValue - orderValue; // Simplified
    
    return pnl;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Trade Logs</CardTitle>
          <CardDescription>Loading recent trade executions...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center justify-between p-3 bg-gray-800 rounded">
                  <div className="flex items-center space-x-3">
                    <div className="h-4 w-4 bg-gray-700 rounded"></div>
                    <div className="space-y-1">
                      <div className="h-4 w-24 bg-gray-700 rounded"></div>
                      <div className="h-3 w-20 bg-gray-700 rounded"></div>
                    </div>
                  </div>
                  <div className="h-6 w-20 bg-gray-700 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Trade Logs</span>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {trades.length} trades
            </Badge>
            {onRefresh && (
              <Button variant="ghost" size="sm" onClick={onRefresh}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardTitle>
        <CardDescription>
          Detailed log of all trade executions and their status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px]">
          {trades.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No trades executed yet</p>
              <p className="text-xs mt-1">Trade executions will appear here when auto-trade is enabled</p>
            </div>
          ) : (
            <div className="space-y-3">
              {trades.map((trade) => {
                const pnl = calculatePnL(trade);
                
                return (
                  <div
                    key={trade.id}
                    className="p-4 bg-gray-800/50 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {getTradeIcon(trade.order_type)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-sm">
                              {trade.order_type} {trade.instrument_symbol}
                            </span>
                            <Badge 
                              variant={trade.trading_mode === 'LIVE' ? 'destructive' : 'secondary'}
                              className="text-xs"
                            >
                              {trade.trading_mode}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-400 mt-1">
                            {trade.instrument_name}
                          </p>
                        </div>
                      </div>
                      {getStatusBadge(trade.order_status, trade.error_message)}
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span className="text-gray-400">Order Price:</span>
                        <div className="font-medium">{formatPrice(trade.order_price)}</div>
                      </div>
                      <div>
                        <span className="text-gray-400">Quantity:</span>
                        <div className="font-medium">{trade.quantity}</div>
                      </div>
                      {trade.execution_price && (
                        <div>
                          <span className="text-gray-400">Execution Price:</span>
                          <div className="font-medium">{formatPrice(trade.execution_price)}</div>
                        </div>
                      )}
                      {trade.execution_quantity && (
                        <div>
                          <span className="text-gray-400">Filled:</span>
                          <div className="font-medium">{trade.execution_quantity}</div>
                        </div>
                      )}
                    </div>

                    {pnl !== null && (
                      <div className="mt-2 pt-2 border-t border-gray-700">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-400">P&L:</span>
                          <span className={`font-medium ${pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {pnl >= 0 ? '+' : ''}{formatPrice(pnl)}
                          </span>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-700 text-xs text-gray-500">
                      <div className="flex items-center space-x-4">
                        <span>{formatDate(trade.created_at)} {formatTime(trade.created_at)}</span>
                        {trade.execution_time && (
                          <span>Executed: {formatTime(trade.execution_time)}</span>
                        )}
                      </div>
                      {trade.dhan_order_id && (
                        <div className="flex items-center space-x-1">
                          <span>Order ID: {trade.dhan_order_id}</span>
                          <ExternalLink className="h-3 w-3" />
                        </div>
                      )}
                    </div>

                    {trade.error_message && (
                      <div className="mt-2 p-2 bg-red-900/20 border border-red-800 rounded text-xs text-red-300">
                        <AlertTriangle className="h-3 w-3 inline mr-1" />
                        {trade.error_message}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
