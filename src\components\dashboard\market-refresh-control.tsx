/**
 * Market Refresh Control Component
 * Provides controls for market data refresh functionality
 */

import { useState } from 'react';
import { RefreshCw, Play, Pause, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MarketRefreshControlProps {
  isAutoRefreshEnabled: boolean;
  onToggleAutoRefresh: () => void;
  onManualRefresh: () => void;
  isRefreshing: boolean;
  lastUpdated: Date | null;
  refreshInterval: number;
}

export function MarketRefreshControl({
  isAutoRefreshEnabled,
  onToggleAutoRefresh,
  onManualRefresh,
  isRefreshing,
  lastUpdated,
  refreshInterval,
}: MarketRefreshControlProps) {
  const [showSettings, setShowSettings] = useState(false);

  const formatRefreshInterval = (interval: number) => {
    const seconds = interval / 1000;
    return seconds >= 60 ? `${seconds / 60}m` : `${seconds}s`;
  };

  return (
    <div className="flex items-center space-x-3">
      {/* Auto Refresh Toggle */}
      <Button
        onClick={onToggleAutoRefresh}
        variant="outline"
        size="sm"
        className={`
          ${isAutoRefreshEnabled 
            ? 'bg-green-900/30 border-green-800 text-green-300 hover:bg-green-900/50' 
            : 'bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700'
          }
        `}
      >
        {isAutoRefreshEnabled ? (
          <>
            <Pause className="h-3 w-3 mr-1" />
            Auto ({formatRefreshInterval(refreshInterval)})
          </>
        ) : (
          <>
            <Play className="h-3 w-3 mr-1" />
            Manual
          </>
        )}
      </Button>

      {/* Manual Refresh */}
      <Button
        onClick={onManualRefresh}
        disabled={isRefreshing}
        variant="outline"
        size="sm"
        className="bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700"
      >
        <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
        Refresh
      </Button>

      {/* Settings */}
      <Button
        onClick={() => setShowSettings(!showSettings)}
        variant="outline"
        size="sm"
        className="bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700"
      >
        <Settings className="h-3 w-3" />
      </Button>

      {/* Last Updated Info */}
      {lastUpdated && (
        <div className="text-xs text-gray-400 hidden sm:block">
          Updated: {lastUpdated.toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Kolkata',
          })}
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="absolute top-full right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-lg z-10 min-w-64">
          <h4 className="text-sm font-medium text-white mb-3">Refresh Settings</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Auto Refresh</span>
              <Button
                onClick={onToggleAutoRefresh}
                size="sm"
                variant={isAutoRefreshEnabled ? "default" : "outline"}
                className="text-xs"
              >
                {isAutoRefreshEnabled ? 'ON' : 'OFF'}
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Interval</span>
              <span className="text-sm text-gray-400">{formatRefreshInterval(refreshInterval)}</span>
            </div>
            <div className="text-xs text-gray-400">
              Market data refreshes automatically during trading hours when auto-refresh is enabled. The system uses intelligent retry with exponential backoff for rate-limited requests.
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
