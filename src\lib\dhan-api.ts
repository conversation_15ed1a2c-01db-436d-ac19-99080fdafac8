/**
 * Dhan API Service Module
 * Handles all Dhan API interactions with proper error handling and authentication
 */

import {
  DhanProfileResponse,
  DhanErrorResponse,
  DhanApiResponse,
  ProcessedProfileData,
  DhanMarketDataResponse,
  ProcessedMarketData,
  MarketDataRequest,
  InstrumentConfig,
  DhanOrderRequest,
  DhanOrderResponse
} from '@/types/dhan';

// Dhan API Configuration
const DHAN_API_BASE_URL = 'https://api.dhan.co/v2';

/**
 * Dhan API Service Class
 */
export class DhanApiService {
  private clientId: string;
  private accessToken: string;

  constructor(clientId: string, accessToken: string) {
    this.clientId = clientId;
    this.accessToken = accessToken;
  }

  /**
   * Make authenticated request to Dhan API
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<DhanApiResponse<T>> {
    try {
      const url = `${DHAN_API_BASE_URL}${endpoint}`;

      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'access-token': this.accessToken,
          ...options.headers,
        },
      });

      // Handle different HTTP status codes
      if (response.status === 401) {
        return {
          success: false,
          error: {
            errorType: 'AUTHENTICATION_ERROR',
            errorCode: 'INVALID_TOKEN',
            errorMessage: 'Access token is invalid or expired. Please check your credentials.',
          },
        };
      }

      if (response.status === 403) {
        return {
          success: false,
          error: {
            errorType: 'AUTHORIZATION_ERROR',
            errorCode: 'ACCESS_DENIED',
            errorMessage: 'Access denied. Your account may not have the required permissions.',
          },
        };
      }

      if (response.status === 429) {
        return {
          success: false,
          error: {
            errorType: 'RATE_LIMIT_ERROR',
            errorCode: 'TOO_MANY_REQUESTS',
            errorMessage: 'Rate limit exceeded. Market data will resume automatically. Please wait before making more requests.',
          },
        };
      }

      if (response.status >= 500) {
        return {
          success: false,
          error: {
            errorType: 'SERVER_ERROR',
            errorCode: 'INTERNAL_ERROR',
            errorMessage: 'Dhan API server error. Please try again later.',
          },
        };
      }

      const data = await response.json();

      if (!response.ok) {
        // Handle Dhan API error response
        const errorResponse: DhanErrorResponse = data;
        return {
          success: false,
          error: {
            ...errorResponse,
            errorMessage: errorResponse.errorMessage || `HTTP ${response.status}: ${response.statusText}`,
          },
        };
      }

      return {
        success: true,
        data: data as T,
      };
    } catch (error) {
      // Handle network or other errors
      let errorMessage = 'Unknown network error';
      let errorCode = 'FETCH_FAILED';

      if (error instanceof TypeError && error.message.includes('fetch')) {
        errorMessage = 'Network connection failed. Please check your internet connection.';
        errorCode = 'NETWORK_ERROR';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: {
          errorType: 'NETWORK_ERROR',
          errorCode,
          errorMessage,
        },
      };
    }
  }

  /**
   * Fetch user profile data from Dhan API
   */
  async getProfile(): Promise<DhanApiResponse<DhanProfileResponse>> {
    return this.makeRequest<DhanProfileResponse>('/profile');
  }

  /**
   * Fetch market data (OHLC) for specified instruments
   */
  async getMarketData(request: MarketDataRequest): Promise<DhanApiResponse<DhanMarketDataResponse>> {
    return this.makeRequest<DhanMarketDataResponse>('/marketfeed/ohlc', {
      method: 'POST',
      headers: {
        'client-id': this.clientId,
      },
      body: JSON.stringify(request),
    });
  }

  /**
   * Fetch live ticker data (LTP) for specified instruments
   */
  async getTickerData(request: MarketDataRequest): Promise<DhanApiResponse<DhanMarketDataResponse>> {
    return this.makeRequest<DhanMarketDataResponse>('/marketfeed/ltp', {
      method: 'POST',
      headers: {
        'client-id': this.clientId,
      },
      body: JSON.stringify(request),
    });
  }

  /**
   * Place a new order
   */
  async placeOrder(orderRequest: DhanOrderRequest): Promise<DhanApiResponse<DhanOrderResponse>> {
    return this.makeRequest<DhanOrderResponse>('/orders', {
      method: 'POST',
      headers: {
        'client-id': this.clientId,
      },
      body: JSON.stringify(orderRequest),
    });
  }

  /**
   * Get order status by order ID
   */
  async getOrderStatus(orderId: string): Promise<DhanApiResponse<DhanOrderResponse>> {
    return this.makeRequest<DhanOrderResponse>(`/orders/${orderId}`);
  }

  /**
   * Get all orders for the day
   */
  async getOrders(): Promise<DhanApiResponse<DhanOrderResponse[]>> {
    return this.makeRequest<DhanOrderResponse[]>('/orders');
  }

  /**
   * Cancel an order
   */
  async cancelOrder(orderId: string): Promise<DhanApiResponse<{ orderId: string; status: string }>> {
    return this.makeRequest<{ orderId: string; status: string }>(`/orders/${orderId}`, {
      method: 'DELETE',
      headers: {
        'client-id': this.clientId,
      },
    });
  }

  /**
   * Process raw profile data for UI consumption
   */
  static processProfileData(rawData: DhanProfileResponse): ProcessedProfileData {
    // Parse token validity (format: "30/03/2025 15:37")
    const tokenExpiry = this.parseDateTime(rawData.tokenValidity);
    const isTokenExpired = tokenExpiry < new Date();

    // Parse data validity (format: "2024-12-05 09:37:52.0")
    const dataExpiry = rawData.dataValidity ? this.parseDataValidity(rawData.dataValidity) : null;
    const isDataExpired = dataExpiry ? dataExpiry < new Date() : false;

    // Parse active segments
    const segments = rawData.activeSegment
      .split(',')
      .map(segment => segment.trim())
      .filter(segment => segment.length > 0);

    return {
      clientId: rawData.dhanClientId,
      tokenExpiry,
      tokenExpiryFormatted: this.formatDateTime(tokenExpiry),
      isTokenExpired,
      segments,
      ddpiStatus: rawData.ddpi === 'Active' ? 'Active' : 'Inactive',
      mtfStatus: rawData.mtf === 'Active' ? 'Active' : 'Inactive',
      dataPlanStatus: rawData.dataPlan === 'Active' ? 'Active' : 'Inactive',
      dataExpiry,
      dataExpiryFormatted: dataExpiry ? this.formatDateTime(dataExpiry) : null,
      isDataExpired,
    };
  }

  /**
   * Parse Dhan date time format (DD/MM/YYYY HH:mm)
   */
  private static parseDateTime(dateTimeStr: string): Date {
    const [datePart, timePart] = dateTimeStr.split(' ');
    const [day, month, year] = datePart.split('/').map(Number);
    const [hours, minutes] = timePart.split(':').map(Number);
    
    return new Date(year, month - 1, day, hours, minutes);
  }

  /**
   * Parse data validity format (YYYY-MM-DD HH:mm:ss.S)
   */
  private static parseDataValidity(dateTimeStr: string): Date {
    // Remove the milliseconds part if present
    const cleanDateStr = dateTimeStr.replace(/\.\d+$/, '');
    return new Date(cleanDateStr);
  }

  /**
   * Format date time for display
   */
  private static formatDateTime(date: Date): string {
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata',
    });
  }

  /**
   * Process raw market data for UI consumption
   */
  static processMarketData(
    rawData: DhanMarketDataResponse,
    instruments: InstrumentConfig[]
  ): ProcessedMarketData[] {
    const processedData: ProcessedMarketData[] = [];

    instruments.forEach(instrument => {
      const exchangeData = rawData.data[instrument.exchange];
      if (!exchangeData) return;

      const instrumentData = exchangeData[instrument.securityId.toString()];
      if (!instrumentData) return;

      const currentPrice = instrumentData.last_price;
      const previousClose = instrumentData.ohlc?.close || currentPrice;
      const change = currentPrice - previousClose;
      const changePercent = previousClose !== 0 ? (change / previousClose) * 100 : 0;

      processedData.push({
        symbol: instrument.symbol,
        name: instrument.name,
        currentPrice,
        change,
        changePercent,
        isPositive: change >= 0,
        open: instrumentData.ohlc?.open,
        high: instrumentData.ohlc?.high,
        low: instrumentData.ohlc?.low,
        close: instrumentData.ohlc?.close,
        volume: instrumentData.volume,
        lastUpdated: new Date(),
        formattedPrice: this.formatPrice(currentPrice),
        formattedChange: this.formatChange(change),
        formattedChangePercent: this.formatChangePercent(changePercent),
      });
    });

    return processedData;
  }

  /**
   * Format price for display
   */
  private static formatPrice(price: number): string {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  }

  /**
   * Format change for display
   */
  private static formatChange(change: number): string {
    const sign = change >= 0 ? '+' : '';
    return sign + new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(change);
  }

  /**
   * Format change percentage for display
   */
  private static formatChangePercent(changePercent: number): string {
    const sign = changePercent >= 0 ? '+' : '';
    return sign + changePercent.toFixed(2) + '%';
  }
}

/**
 * Create Dhan API service instance with environment credentials
 */
export function createDhanApiService(): DhanApiService | null {
  const clientId = process.env.DHAN_CLIENT_ID;
  const accessToken = process.env.DHAN_ACCESS_TOKEN;

  if (!clientId || !accessToken) {
    console.error('Dhan API credentials not found in environment variables');
    return null;
  }

  return new DhanApiService(clientId, accessToken);
}

/**
 * Server-side function to fetch profile data
 * This should be called from API routes to keep credentials secure
 */
export async function fetchDhanProfile(): Promise<DhanApiResponse<ProcessedProfileData>> {
  const apiService = createDhanApiService();

  if (!apiService) {
    return {
      success: false,
      error: {
        errorType: 'CONFIGURATION_ERROR',
        errorCode: 'MISSING_CREDENTIALS',
        errorMessage: 'Dhan API credentials not configured',
      },
    };
  }

  const response = await apiService.getProfile();

  if (!response.success || !response.data) {
    return {
      success: false,
      error: response.error,
    };
  }

  const processedData = DhanApiService.processProfileData(response.data);

  return {
    success: true,
    data: processedData,
  };
}

/**
 * Default instruments configuration for NIFTY 50 and Silver
 */
export const DEFAULT_INSTRUMENTS: InstrumentConfig[] = [
  {
    symbol: 'NIFTY50',
    name: 'NIFTY 50',
    exchange: 'NSE', // NSE exchange for NIFTY 50
    securityId: 2, // Correct NIFTY index security ID from Dhan instrument master
  },
  {
    symbol: 'SILVER',
    name: 'Silver',
    exchange: 'MCX_COMM', // MCX Commodity exchange for Silver futures (from Dhan API documentation)
    securityId: 439488, // Correct Silver future security ID from Dhan instrument master
  },
];

/**
 * Server-side function to fetch market data
 * This should be called from API routes to keep credentials secure
 */
export async function fetchDhanMarketData(
  instruments: InstrumentConfig[] = DEFAULT_INSTRUMENTS
): Promise<DhanApiResponse<ProcessedMarketData[]>> {
  console.log('🔍 fetchDhanMarketData: Starting with instruments:', instruments);

  const apiService = createDhanApiService();

  if (!apiService) {
    console.error('❌ fetchDhanMarketData: API service creation failed');
    return {
      success: false,
      error: {
        errorType: 'CONFIGURATION_ERROR',
        errorCode: 'MISSING_CREDENTIALS',
        errorMessage: 'Dhan API credentials not configured',
      },
    };
  }

  // Build request object
  const request: MarketDataRequest = {};
  instruments.forEach(instrument => {
    if (!request[instrument.exchange]) {
      request[instrument.exchange] = [];
    }
    request[instrument.exchange].push(instrument.securityId);
  });

  console.log('🔍 fetchDhanMarketData: Built request:', JSON.stringify(request, null, 2));

  const response = await apiService.getMarketData(request);

  console.log('🔍 fetchDhanMarketData: API response:', {
    success: response.success,
    hasData: !!response.data,
    error: response.error
  });

  if (!response.success || !response.data) {
    console.error('❌ fetchDhanMarketData: API request failed:', response.error);
    return {
      success: false,
      error: response.error,
    };
  }

  console.log('🔍 fetchDhanMarketData: Raw API data:', JSON.stringify(response.data, null, 2));

  const processedData = DhanApiService.processMarketData(response.data, instruments);

  console.log('🔍 fetchDhanMarketData: Processed data:', {
    count: processedData.length,
    symbols: processedData.map(d => d.symbol)
  });

  return {
    success: true,
    data: processedData,
  };
}
