/**
 * Trading Logger Service
 * Comprehensive logging system for trading signals, executions, and audit trail
 */

import { supabase } from '@/lib/supabase';
import { TradingSignal } from './strategies/strategy-service';

export interface LogEntry {
  id?: string;
  userId: string;
  logLevel: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  category: 'SIGNAL' | 'TRADE' | 'STRATEGY' | 'SYSTEM' | 'API';
  message: string;
  metadata?: Record<string, unknown>;
  correlationId?: string;
  timestamp: Date;
}

export interface AuditTrail {
  id?: string;
  userId: string;
  action: string;
  entityType: 'SIGNAL' | 'TRADE' | 'CONFIG' | 'USER';
  entityId: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export class TradingLogger {
  private static instance: TradingLogger;
  private logBuffer: LogEntry[] = [];
  private auditBuffer: AuditTrail[] = [];
  private readonly BUFFER_SIZE = 100;
  private readonly FLUSH_INTERVAL = 30000; // 30 seconds

  private constructor() {
    // Start periodic flush
    setInterval(() => {
      this.flushLogs();
    }, this.FLUSH_INTERVAL);

    // Flush on page unload
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flushLogs();
      });
    }
  }

  static getInstance(): TradingLogger {
    if (!TradingLogger.instance) {
      TradingLogger.instance = new TradingLogger();
    }
    return TradingLogger.instance;
  }

  /**
   * Log a trading signal generation
   */
  logSignal(userId: string, signal: TradingSignal, metadata?: Record<string, unknown>): void {
    this.log({
      userId,
      logLevel: 'INFO',
      category: 'SIGNAL',
      message: `${signal.signalType} signal generated for ${signal.instrumentSymbol} at ₹${signal.signalPrice}`,
      metadata: {
        signalId: signal.id,
        instrumentSymbol: signal.instrumentSymbol,
        signalType: signal.signalType,
        signalPrice: signal.signalPrice,
        strategyName: signal.strategyName,
        autoTradeEnabled: signal.autoTradeEnabled,
        tradingMode: signal.tradingMode,
        ...metadata
      },
      correlationId: signal.id,
      timestamp: new Date()
    });
  }

  /**
   * Log a trade execution attempt
   */
  logTradeAttempt(userId: string, tradeData: Record<string, unknown>, correlationId: string): void {
    this.log({
      userId,
      logLevel: 'INFO',
      category: 'TRADE',
      message: `Trade execution attempted: ${tradeData.orderType} ${tradeData.instrumentSymbol}`,
      metadata: {
        orderType: tradeData.orderType,
        instrumentSymbol: tradeData.instrumentSymbol,
        price: tradeData.price,
        quantity: tradeData.quantity,
        tradingMode: tradeData.tradingMode
      },
      correlationId,
      timestamp: new Date()
    });
  }

  /**
   * Log a successful trade execution
   */
  logTradeSuccess(userId: string, tradeResult: Record<string, unknown>, correlationId: string): void {
    this.log({
      userId,
      logLevel: 'INFO',
      category: 'TRADE',
      message: `Trade executed successfully: Order ID ${tradeResult.orderId}`,
      metadata: {
        orderId: tradeResult.orderId,
        status: tradeResult.status,
        executionPrice: tradeResult.executionPrice,
        executionQuantity: tradeResult.executionQuantity,
        tradingMode: tradeResult.tradingMode
      },
      correlationId,
      timestamp: new Date()
    });
  }

  /**
   * Log a failed trade execution
   */
  logTradeFailure(userId: string, error: string, tradeData: Record<string, unknown>, correlationId: string): void {
    this.log({
      userId,
      logLevel: 'ERROR',
      category: 'TRADE',
      message: `Trade execution failed: ${error}`,
      metadata: {
        error,
        orderType: tradeData.orderType,
        instrumentSymbol: tradeData.instrumentSymbol,
        price: tradeData.price,
        quantity: tradeData.quantity,
        tradingMode: tradeData.tradingMode
      },
      correlationId,
      timestamp: new Date()
    });
  }

  /**
   * Log strategy performance updates
   */
  logStrategyPerformance(userId: string, performanceData: Record<string, unknown>): void {
    this.log({
      userId,
      logLevel: 'INFO',
      category: 'STRATEGY',
      message: `Strategy performance updated`,
      metadata: {
        strategyName: performanceData.strategyName,
        totalSignals: performanceData.totalSignals,
        executedTrades: performanceData.executedTrades,
        winRate: performanceData.winRate,
        totalPnl: performanceData.totalPnl
      },
      timestamp: new Date()
    });
  }

  /**
   * Log system events
   */
  logSystem(userId: string, message: string, level: 'INFO' | 'WARN' | 'ERROR' = 'INFO', metadata?: Record<string, unknown>): void {
    this.log({
      userId,
      logLevel: level,
      category: 'SYSTEM',
      message,
      metadata,
      timestamp: new Date()
    });
  }

  /**
   * Log API calls and responses
   */
  logApiCall(userId: string, endpoint: string, method: string, status: number, responseTime: number, error?: string): void {
    this.log({
      userId,
      logLevel: error ? 'ERROR' : 'INFO',
      category: 'API',
      message: `API ${method} ${endpoint} - ${status} (${responseTime}ms)`,
      metadata: {
        endpoint,
        method,
        status,
        responseTime,
        error
      },
      timestamp: new Date()
    });
  }

  /**
   * Create audit trail entry
   */
  audit(auditEntry: Omit<AuditTrail, 'timestamp'>): void {
    this.auditBuffer.push({
      ...auditEntry,
      timestamp: new Date()
    });

    if (this.auditBuffer.length >= this.BUFFER_SIZE) {
      this.flushAuditTrail();
    }
  }

  /**
   * Add log entry to buffer
   */
  private log(entry: LogEntry): void {
    // Also log to console for development
    if (process.env.NODE_ENV === 'development') {
      const logMethod = entry.logLevel === 'ERROR' ? console.error : 
                       entry.logLevel === 'WARN' ? console.warn : console.log;
      logMethod(`[${entry.category}] ${entry.message}`, entry.metadata);
    }

    this.logBuffer.push(entry);

    if (this.logBuffer.length >= this.BUFFER_SIZE) {
      this.flushLogs();
    }
  }

  /**
   * Flush log buffer to database
   */
  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // Create trading_logs table if it doesn't exist
      await this.ensureLogsTable();

      const { error } = await supabase
        .from('trading_logs')
        .insert(logsToFlush.map(log => ({
          user_id: log.userId,
          log_level: log.logLevel,
          category: log.category,
          message: log.message,
          metadata: log.metadata,
          correlation_id: log.correlationId,
          created_at: log.timestamp.toISOString()
        })));

      if (error) {
        console.error('Failed to flush logs to database:', error);
        // Re-add logs to buffer for retry
        this.logBuffer.unshift(...logsToFlush);
      }
    } catch (error) {
      console.error('Error flushing logs:', error);
      // Re-add logs to buffer for retry
      this.logBuffer.unshift(...logsToFlush);
    }
  }

  /**
   * Flush audit trail buffer to database
   */
  private async flushAuditTrail(): Promise<void> {
    if (this.auditBuffer.length === 0) return;

    const auditToFlush = [...this.auditBuffer];
    this.auditBuffer = [];

    try {
      // Create audit_trail table if it doesn't exist
      await this.ensureAuditTable();

      const { error } = await supabase
        .from('audit_trail')
        .insert(auditToFlush.map(audit => ({
          user_id: audit.userId,
          action: audit.action,
          entity_type: audit.entityType,
          entity_id: audit.entityId,
          old_values: audit.oldValues,
          new_values: audit.newValues,
          ip_address: audit.ipAddress,
          user_agent: audit.userAgent,
          created_at: audit.timestamp.toISOString()
        })));

      if (error) {
        console.error('Failed to flush audit trail to database:', error);
        // Re-add audit entries to buffer for retry
        this.auditBuffer.unshift(...auditToFlush);
      }
    } catch (error) {
      console.error('Error flushing audit trail:', error);
      // Re-add audit entries to buffer for retry
      this.auditBuffer.unshift(...auditToFlush);
    }
  }

  /**
   * Ensure logs table exists
   */
  private async ensureLogsTable(): Promise<void> {
    // This would typically be handled by migrations, but for completeness
    // we can check if the table exists and create it if needed
  }

  /**
   * Ensure audit table exists
   */
  private async ensureAuditTable(): Promise<void> {
    // This would typically be handled by migrations, but for completeness
    // we can check if the table exists and create it if needed
  }

  /**
   * Get recent logs for a user
   */
  async getLogs(userId: string, limit: number = 100, category?: string): Promise<LogEntry[]> {
    try {
      let query = supabase
        .from('trading_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching logs:', error);
        return [];
      }

      return data.map(log => ({
        id: log.id,
        userId: log.user_id,
        logLevel: log.log_level,
        category: log.category,
        message: log.message,
        metadata: log.metadata,
        correlationId: log.correlation_id,
        timestamp: new Date(log.created_at)
      }));
    } catch (error) {
      console.error('Error in getLogs:', error);
      return [];
    }
  }

  /**
   * Get audit trail for a user
   */
  async getAuditTrail(userId: string, limit: number = 100): Promise<AuditTrail[]> {
    try {
      const { data, error } = await supabase
        .from('audit_trail')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching audit trail:', error);
        return [];
      }

      return data.map(audit => ({
        id: audit.id,
        userId: audit.user_id,
        action: audit.action,
        entityType: audit.entity_type,
        entityId: audit.entity_id,
        oldValues: audit.old_values,
        newValues: audit.new_values,
        ipAddress: audit.ip_address,
        userAgent: audit.user_agent,
        timestamp: new Date(audit.created_at)
      }));
    } catch (error) {
      console.error('Error in getAuditTrail:', error);
      return [];
    }
  }
}

// Export singleton instance
export const tradingLogger = TradingLogger.getInstance();
