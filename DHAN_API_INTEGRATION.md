# Dhan API Integration

This document describes the Dhan API integration implemented in the dashboard feature.

## Overview

The dashboard now includes integration with the Dhan API to display user profile information including:

- Client ID and account details
- Token validity and expiration status
- Active trading segments (Equity, Derivative, Currency, Commodity)
- Account status (DDPI, MTF, Data Plan)
- Data plan validity

## Features Implemented

### ✅ Completed Features

1. **Secure API Integration**
   - Server-side API calls to protect credentials
   - Environment variable configuration
   - JWT token authentication

2. **Profile Data Display**
   - User-friendly profile card component
   - Real-time token expiration checking
   - Active segments visualization
   - Account status indicators

3. **Error Handling**
   - Comprehensive error handling for different API failure scenarios
   - Network error detection and user feedback
   - Authentication error handling
   - Rate limiting error handling
   - Demo mode for expired tokens

4. **User Experience**
   - Loading states with skeleton components
   - Error boundaries for crash protection
   - Toast notifications for user feedback
   - Retry functionality for failed requests

5. **Type Safety**
   - Full TypeScript integration
   - Type-safe API responses
   - Interface definitions for all data structures

## File Structure

```
src/
├── types/
│   └── dhan.ts                     # TypeScript interfaces for Dhan API
├── lib/
│   └── dhan-api.ts                 # Dhan API service and utilities
├── hooks/
│   └── use-dhan-profile.ts         # React hook for profile data
├── components/
│   ├── dashboard/
│   │   ├── profile-card.tsx        # Main profile display component
│   │   ├── profile-error.tsx       # Error state component
│   │   ├── profile-loading.tsx     # Loading state component
│   │   └── demo-profile-data.tsx   # Demo data for expired tokens
│   ├── ui/
│   │   └── toast.tsx               # Toast notification system
│   ├── providers/
│   │   └── toast-provider.tsx      # Toast context provider
│   └── error-boundary.tsx          # Error boundary component
├── app/
│   ├── api/dhan/profile/
│   │   └── route.ts                # Server-side API endpoint
│   └── dashboard/
│       └── page.tsx                # Updated dashboard page
└── __tests__/
    └── dhan-api.test.ts            # Unit tests for API integration
```

## Configuration

### Environment Variables

Add the following to your `.env.local` file:

```env
# Dhan API Configuration (Server-side only)
DHAN_CLIENT_ID=your_client_id_here
DHAN_ACCESS_TOKEN=your_access_token_here
```

### Getting Dhan API Credentials

1. Login to [web.dhan.co](https://web.dhan.co)
2. Go to **My Profile** → **Access DhanHQ APIs**
3. Generate a new access token
4. Copy your Client ID and Access Token
5. Add them to your environment variables

## API Endpoints

### GET /api/dhan/profile

Fetches user profile data from Dhan API.

**Response Format:**
```json
{
  "success": true,
  "data": {
    "clientId": "1000237632",
    "tokenExpiry": "2025-06-21T15:37:00.000Z",
    "tokenExpiryFormatted": "Jun 21, 2025, 3:37 PM",
    "isTokenExpired": false,
    "segments": ["Equity", "Derivative", "Currency", "Commodity"],
    "ddpiStatus": "Active",
    "mtfStatus": "Active",
    "dataPlanStatus": "Active",
    "dataExpiry": "2024-12-05T09:37:52.000Z",
    "dataExpiryFormatted": "Dec 5, 2024, 9:37 AM",
    "isDataExpired": true
  }
}
```

## Error Handling

The integration handles various error scenarios:

1. **Authentication Errors (401)**
   - Invalid or expired access tokens
   - Shows demo data with instructions to refresh token

2. **Network Errors**
   - Connection failures
   - Timeout issues
   - DNS resolution problems

3. **Rate Limiting (429)**
   - Too many requests
   - Automatic retry with backoff

4. **Server Errors (5xx)**
   - Dhan API server issues
   - Graceful degradation

## Security Considerations

- **Server-side API calls**: Credentials are never exposed to the client
- **Environment variables**: Sensitive data stored securely
- **Error handling**: No sensitive information leaked in error messages
- **Token management**: Automatic expiration detection

## Testing

Run the test suite:

```bash
npm test
```

The tests cover:
- API service functionality
- Error handling scenarios
- Data processing logic
- Component rendering

## Demo Mode

When the API token is expired or invalid, the dashboard automatically switches to demo mode, showing:
- Sample profile data
- Token expiration notice
- Instructions for refreshing credentials

## Future Enhancements

Potential improvements for the integration:

1. **Token Refresh**: Automatic token renewal
2. **Caching**: Profile data caching with TTL
3. **Real-time Updates**: WebSocket integration for live data
4. **Additional Endpoints**: Portfolio, orders, and market data
5. **Offline Support**: Cached data for offline viewing

## Troubleshooting

### Common Issues

1. **401 Authentication Error**
   - Check if access token is valid and not expired
   - Verify client ID matches the token
   - Generate new token from Dhan dashboard

2. **Network Connection Issues**
   - Check internet connectivity
   - Verify Dhan API endpoints are accessible
   - Check firewall/proxy settings

3. **Environment Variables Not Loading**
   - Ensure `.env.local` file exists in project root
   - Restart development server after changes
   - Check variable names match exactly

### Support

For Dhan API specific issues, refer to:
- [Dhan API Documentation](https://dhanhq.co/docs/v2/)
- Dhan support channels
- API status page
