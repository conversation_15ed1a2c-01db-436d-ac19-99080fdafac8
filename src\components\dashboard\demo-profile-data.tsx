/**
 * Demo Profile Data Component
 * Shows sample profile data when API is not available
 */

import { ProcessedProfileData } from '@/types/dhan';
import { ProfileCard } from './profile-card';
import { AlertTriangle, Info } from 'lucide-react';

const demoProfileData: ProcessedProfileData = {
  clientId: '1000237632',
  tokenExpiry: new Date('2025-06-21T15:37:00'),
  tokenExpiryFormatted: 'Jun 21, 2025, 3:37 PM',
  isTokenExpired: false,
  segments: ['Equity', 'Derivative', 'Currency', 'Commodity'],
  ddpiStatus: 'Active',
  mtfStatus: 'Active',
  dataPlanStatus: 'Active',
  dataExpiry: new Date('2024-12-05T09:37:52'),
  dataExpiryFormatted: 'Dec 5, 2024, 9:37 AM',
  isDataExpired: true,
};

export function DemoProfileData() {
  return (
    <div className="space-y-4">
      {/* Demo Notice */}
      <div className="bg-blue-900/20 border border-blue-800 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-blue-300">Demo Mode</h3>
            <p className="text-sm text-blue-200">
              The Dhan API token has expired. Showing demo profile data to demonstrate the dashboard functionality.
            </p>
            <p className="text-xs text-blue-300">
              To use live data, please update the access token in your environment variables.
            </p>
          </div>
        </div>
      </div>

      {/* Demo Profile Card */}
      <ProfileCard profile={demoProfileData} />

      {/* Token Expiration Notice */}
      <div className="bg-yellow-900/20 border border-yellow-800 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-yellow-300">Token Management</h3>
            <p className="text-sm text-yellow-200">
              Dhan API tokens have expiration dates. When a token expires, you&apos;ll need to generate a new one from your Dhan account.
            </p>
            <div className="text-xs text-yellow-300 space-y-1">
              <p>• Login to web.dhan.co</p>
              <p>• Go to My Profile → Access DhanHQ APIs</p>
              <p>• Generate a new access token</p>
              <p>• Update your environment variables</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
