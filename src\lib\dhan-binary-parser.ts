/**
 * Dhan Binary Message Parser
 * Handles parsing of binary messages from Dhan WebSocket API according to v2 specification
 */

import { DhanResponseHeader, DhanTickerData, DhanQuoteData, DhanPrevCloseData } from './websocket-market-data';
import { ProcessedMarketData } from '@/types/dhan';

export interface ParsedBinaryMessage {
  header: DhanResponseHeader;
  disconnectionCode?: number;
  data?: DhanTickerData | DhanQuoteData | DhanPrevCloseData;
  rawData?: ArrayBuffer;
}

// Exchange segment mappings (from Dhan API documentation)
export const EXCHANGE_SEGMENTS = {
  1: 'NSE_EQ',
  2: 'NSE_FNO', 
  3: 'NSE_CURRENCY',
  4: 'BSE_EQ',
  5: 'BSE_FNO',
  6: 'BSE_CURRENCY',
  7: 'MCX_COMM',
  8: 'NCDEX_COMM'
} as const;

// Feed response codes (from Dhan API documentation)
export const FEED_RESPONSE_CODES = {
  1: 'INDEX_DATA',
  2: 'TICKER_DATA',
  3: 'MARKET_DEPTH',
  4: 'QUOTE_DATA',
  5: 'OI_DATA',
  6: 'PREV_CLOSE',
  7: 'MARKET_STATUS',
  8: 'FULL_PACKET',
  50: 'DISCONNECTION'
} as const;

// Feed request codes for subscription
export const FEED_REQUEST_CODES = {
  CONNECT: 11,
  DISCONNECT: 12,
  SUBSCRIBE_TICKER: 15,
  SUBSCRIBE_QUOTE: 16,
  SUBSCRIBE_FULL: 17
} as const;

/**
 * Parse binary response header (8 bytes)
 */
export function parseResponseHeader(buffer: ArrayBuffer, offset = 0): DhanResponseHeader {
  const view = new DataView(buffer, offset);
  
  return {
    feedResponseCode: view.getUint8(0),
    messageLength: view.getUint16(1, false), // Big endian
    exchangeSegment: view.getUint8(3),
    securityId: view.getUint32(4, false) // Big endian
  };
}

/**
 * Parse ticker data packet (Response code 2)
 */
export function parseTickerData(buffer: ArrayBuffer): DhanTickerData {
  const header = parseResponseHeader(buffer, 0);
  const view = new DataView(buffer, 8); // Skip header
  
  return {
    header,
    lastTradedPrice: view.getFloat32(0, false), // Big endian
    lastTradeTime: view.getUint32(4, false) // Big endian
  };
}

/**
 * Parse quote data packet (Response code 4)
 */
export function parseQuoteData(buffer: ArrayBuffer): DhanQuoteData {
  const header = parseResponseHeader(buffer, 0);
  const view = new DataView(buffer, 8); // Skip header
  
  return {
    header,
    lastTradedPrice: view.getFloat32(0, false),
    lastTradedQuantity: view.getUint16(4, false),
    lastTradeTime: view.getUint32(6, false),
    averageTradePrice: view.getFloat32(10, false),
    volume: view.getUint32(14, false),
    totalSellQuantity: view.getUint32(18, false),
    totalBuyQuantity: view.getUint32(22, false),
    dayOpen: view.getFloat32(26, false),
    dayClose: view.getFloat32(30, false),
    dayHigh: view.getFloat32(34, false),
    dayLow: view.getFloat32(38, false)
  };
}

/**
 * Parse previous close data packet (Response code 6)
 */
export function parsePrevCloseData(buffer: ArrayBuffer): DhanPrevCloseData {
  const header = parseResponseHeader(buffer, 0);
  const view = new DataView(buffer, 8); // Skip header
  
  return {
    header,
    previousClose: view.getFloat32(0, false),
    openInterest: view.getUint32(4, false)
  };
}

/**
 * Create binary authorization message
 */
export function createAuthMessage(clientId: string, accessToken: string): ArrayBuffer {
  const buffer = new ArrayBuffer(616); // Total size as per Dhan spec
  const view = new DataView(buffer);
  const encoder = new TextEncoder();
  
  // Header (84 bytes)
  view.setUint8(0, 11); // Feed request code for connect
  view.setUint16(1, 616, false); // Message length (big endian)
  
  // Client ID (30 bytes, starting at byte 3)
  const clientIdBytes = encoder.encode(clientId.padEnd(30, '\0'));
  new Uint8Array(buffer, 3, 30).set(clientIdBytes.slice(0, 30));
  
  // Dhan Auth (50 bytes, starting at byte 33) - pass as zero
  // Already initialized to zero
  
  // Access Token (500 bytes, starting at byte 84)
  const tokenBytes = encoder.encode(accessToken.padEnd(500, '\0'));
  new Uint8Array(buffer, 84, 500).set(tokenBytes.slice(0, 500));
  
  // Mobile number (20 bytes, starting at byte 584) - optional, pass as zero
  // Already initialized to zero
  
  // Auth type (2 bytes, starting at byte 604)
  view.setUint16(604, 2, false); // Auth type 2
  
  // Version (10 bytes, starting at byte 606) - pass as zero
  // Already initialized to zero
  
  return buffer;
}

/**
 * Create binary subscription message for instruments
 */
export function createSubscriptionMessage(
  requestCode: number,
  instruments: Array<{ exchangeSegment: string; securityId: string }>
): ArrayBuffer {
  const buffer = new ArrayBuffer(2188); // Header (84) + instrument count (4) + instruments (2100)
  const view = new DataView(buffer);
  
  // Header
  view.setUint8(0, requestCode); // Feed request code
  view.setUint16(1, 2188, false); // Message length
  
  // Client ID and other header fields would be set by the WebSocket service
  
  // Instrument count (4 bytes at offset 84)
  view.setUint32(84, instruments.length, false);
  
  // Instruments (up to 100 instruments, 21 bytes each, starting at offset 88)
  let offset = 88;
  for (let i = 0; i < Math.min(instruments.length, 100); i++) {
    const instrument = instruments[i];
    
    // Exchange segment (1 byte)
    const exchangeCode = getExchangeCode(instrument.exchangeSegment);
    view.setUint8(offset, exchangeCode);
    
    // Security ID (20 bytes)
    const encoder = new TextEncoder();
    const securityIdBytes = encoder.encode(instrument.securityId.padEnd(20, '\0'));
    new Uint8Array(buffer, offset + 1, 20).set(securityIdBytes.slice(0, 20));
    
    offset += 21;
  }
  
  return buffer;
}

/**
 * Get exchange code from exchange segment string
 */
function getExchangeCode(exchangeSegment: string): number {
  const reverseMapping: Record<string, number> = {};
  for (const [code, segment] of Object.entries(EXCHANGE_SEGMENTS)) {
    reverseMapping[segment] = parseInt(code);
  }
  return reverseMapping[exchangeSegment] || 1; // Default to NSE_EQ
}

/**
 * Create disconnect message
 */
export function createDisconnectMessage(): ArrayBuffer {
  const message = JSON.stringify({ RequestCode: 12 });
  const encoder = new TextEncoder();
  return encoder.encode(message).buffer;
}

/**
 * Parse binary message and return appropriate data structure
 */
export function parseBinaryMessage(buffer: ArrayBuffer): ParsedBinaryMessage {
  if (buffer.byteLength < 8) {
    throw new Error('Invalid message: too short for header');
  }
  
  const header = parseResponseHeader(buffer);
  
  switch (header.feedResponseCode) {
    case 2: // Ticker data
      return parseTickerData(buffer);
    case 4: // Quote data
      return parseQuoteData(buffer);
    case 6: // Previous close
      return parsePrevCloseData(buffer);
    case 50: // Disconnection
      const view = new DataView(buffer, 8);
      return {
        header,
        disconnectionCode: view.getUint16(0, false)
      };
    default:
      console.warn(`Unknown feed response code: ${header.feedResponseCode}`);
      return { header, rawData: buffer };
  }
}

/**
 * Convert parsed data to ProcessedMarketData format
 */
export function convertToProcessedMarketData(
  parsedData: DhanTickerData | DhanQuoteData,
  instrumentMap: Map<string, { symbol: string; name: string; previousClose?: number }>
): ProcessedMarketData | null {
  const { header } = parsedData;
  const exchangeSegment = EXCHANGE_SEGMENTS[header.exchangeSegment as keyof typeof EXCHANGE_SEGMENTS];
  const instrumentKey = `${exchangeSegment}_${header.securityId}`;
  const instrumentInfo = instrumentMap.get(instrumentKey);
  
  if (!instrumentInfo) {
    console.warn(`Unknown instrument: ${instrumentKey}`);
    return null;
  }
  
  const currentPrice = 'lastTradedPrice' in parsedData ? parsedData.lastTradedPrice : 0;
  const previousClose = instrumentInfo.previousClose || currentPrice;
  const change = currentPrice - previousClose;
  const changePercent = previousClose > 0 ? (change / previousClose) * 100 : 0;
  
  const baseData: ProcessedMarketData = {
    symbol: instrumentInfo.symbol,
    name: instrumentInfo.name,
    currentPrice,
    change,
    changePercent,
    isPositive: change >= 0,
    lastUpdated: new Date(),
    formattedPrice: currentPrice.toFixed(2),
    formattedChange: change.toFixed(2),
    formattedChangePercent: `${changePercent.toFixed(2)}%`
  };
  
  // Add additional data if available (quote data)
  if ('dayOpen' in parsedData) {
    return {
      ...baseData,
      open: parsedData.dayOpen,
      high: parsedData.dayHigh,
      low: parsedData.dayLow,
      close: parsedData.dayClose,
      volume: parsedData.volume
    };
  }
  
  return baseData;
}
