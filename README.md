# Supabase Auth Clone - Modern Authentication System

A modern, production-ready authentication system inspired by Supabase's sign-in page design. Built with Next.js 14, TypeScript, Tailwind CSS, and Supabase Auth.

![Supabase Auth Clone](https://img.shields.io/badge/Next.js-14-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4-38B2AC?style=for-the-badge&logo=tailwind-css)
![Supabase](https://img.shields.io/badge/Supabase-Auth-green?style=for-the-badge&logo=supabase)

## ✨ Features

### 🎨 **Modern UI/UX**
- **Pixel-perfect clone** of Supabase's sign-in page design
- **Dark theme** with green accent colors and subtle gradients
- **Responsive design** that works seamlessly across all devices
- **Smooth animations** and micro-interactions using Framer Motion

### 🔐 **Authentication Features**
- **Email/Password authentication** with robust validation
- **Social login** support (GitHub, SSO)
- **Password reset** functionality
- **User registration** with email confirmation
- **Form validation** using React Hook Form + Zod
- **Real-time password strength indicator**

### ♿ **Accessibility & Security**
- **WCAG 2.1 compliant** with proper ARIA labels and roles
- **Keyboard navigation** support
- **Screen reader friendly**
- **Form validation** with descriptive error messages
- **Loading states** and user feedback
- **Secure authentication** using Supabase Auth

### 🚀 **Modern Tech Stack**
- **Next.js 14** with App Router and TypeScript
- **Tailwind CSS 4** for utility-first styling
- **Framer Motion** for smooth animations
- **React Hook Form** for performant form handling
- **Zod** for runtime type validation
- **Supabase** for authentication backend

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **Authentication**: Supabase Auth
- **Form Handling**: React Hook Form + Zod
- **Animations**: Framer Motion
- **UI Components**: Custom components with Headless UI
- **Icons**: Lucide React

## 📦 Installation

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- A Supabase account (for authentication)

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/supabase-auth-clone.git
cd supabase-auth-clone
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Set Up Supabase

1. **Create a new Supabase project** at [supabase.com](https://supabase.com)
2. **Go to Settings > API** in your Supabase dashboard
3. **Copy your project URL and anon key**

### 4. Configure Environment Variables

Create a `.env.local` file in the root directory:

```bash
cp .env.local.example .env.local
```

Update the `.env.local` file with your Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. Configure Authentication Providers (Optional)

To enable social login (GitHub, SSO), configure the providers in your Supabase dashboard:

1. **Go to Authentication > Providers** in your Supabase dashboard
2. **Enable GitHub provider** and add your GitHub OAuth app credentials
3. **Configure redirect URLs**:
   - Development: `http://localhost:3000/auth/callback`
   - Production: `https://yourdomain.com/auth/callback`

## 🚀 Getting Started

### Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

### Build for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
supabase-auth-clone/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── dashboard/          # Protected dashboard page
│   │   ├── forgot-password/    # Password reset page
│   │   ├── sign-up/           # User registration page
│   │   ├── globals.css        # Global styles and CSS variables
│   │   ├── layout.tsx         # Root layout component
│   │   └── page.tsx           # Sign-in page (home)
│   ├── components/            # Reusable components
│   │   ├── auth/              # Authentication components
│   │   │   ├── sign-in-form.tsx
│   │   │   ├── sign-up-form.tsx
│   │   │   └── forgot-password-form.tsx
│   │   ├── layout/            # Layout components
│   │   │   ├── header.tsx
│   │   │   └── testimonial.tsx
│   │   └── ui/                # Base UI components
│   │       ├── button.tsx
│   │       ├── input.tsx
│   │       └── label.tsx
│   └── lib/                   # Utility functions
│       ├── supabase.ts        # Supabase client configuration
│       └── utils.ts           # Utility functions
├── public/                    # Static assets
├── .env.local.example         # Environment variables template
├── tailwind.config.js         # Tailwind CSS configuration
└── package.json              # Dependencies and scripts
```

## 🎯 Key Features Implemented

### 🔍 **Design Analysis & Replication**
- **Analyzed Supabase's sign-in page** to understand UI/UX patterns
- **Replicated visual design** including layout, colors, typography, and spacing
- **Enhanced with modern animations** and improved user experience

### 🎨 **UI/UX Enhancements**
- **Smooth page transitions** with staggered animations
- **Interactive button states** with hover and tap effects
- **Form validation feedback** with real-time error display
- **Loading states** with animated spinners
- **Password strength indicator** for better UX
- **Success/error states** with appropriate visual feedback

### ♿ **Accessibility Features**
- **ARIA labels and roles** for screen readers
- **Keyboard navigation** support
- **Focus management** and visual indicators
- **Error announcements** with `aria-live` regions
- **Semantic HTML** structure
- **Color contrast** compliance

### 🔒 **Security Measures**
- **Client-side validation** with Zod schemas
- **Password strength requirements**
- **CSRF protection** via Supabase Auth
- **Secure token handling**
- **Environment variable protection**

## 🚀 Deployment

### Vercel (Recommended)

1. **Push your code** to a GitHub repository
2. **Connect your repository** to Vercel
3. **Add environment variables** in Vercel dashboard
4. **Deploy** - Vercel will automatically build and deploy

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- **Netlify**
- **Railway**
- **DigitalOcean App Platform**
- **AWS Amplify**

## 🧪 Testing

### Manual Testing Checklist

- [ ] **Sign-in form** validation and submission
- [ ] **Sign-up form** with password strength indicator
- [ ] **Forgot password** flow
- [ ] **Social login** (GitHub) integration
- [ ] **Responsive design** across devices
- [ ] **Accessibility** with screen readers
- [ ] **Keyboard navigation**
- [ ] **Loading states** and error handling

### Automated Testing (Future Enhancement)

Consider adding:
- **Unit tests** with Jest and React Testing Library
- **Integration tests** for authentication flows
- **E2E tests** with Playwright or Cypress
- **Accessibility tests** with axe-core

## 🎨 Customization

### Theme Customization

The application uses CSS custom properties for theming. Update `src/app/globals.css`:

```css
:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #10b981;
  /* ... other variables */
}
```

### Component Customization

All components are built with customization in mind:
- **Tailwind classes** can be overridden
- **Framer Motion** animations can be adjusted
- **Form validation** schemas can be modified

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit your changes**: `git commit -m 'Add amazing feature'`
4. **Push to the branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Supabase** for the original design inspiration
- **Next.js team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **Framer Motion** for smooth animations

## 📞 Support

If you have any questions or need help setting up the project:

1. **Check the documentation** above
2. **Open an issue** on GitHub
3. **Join the discussion** in GitHub Discussions

---

**Built with ❤️ by [Your Name]**

*This project is not affiliated with Supabase. It's an educational project inspired by their excellent design.*
