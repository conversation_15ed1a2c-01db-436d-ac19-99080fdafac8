/**
 * Sandbox Trading Service
 * Simulates trading operations without real money for testing and development
 */

import { ProcessedOrderData } from '@/types/dhan';
import { tradingLogger } from './trading-logger';

export interface SandboxConfig {
  successRate: number; // 0-1, probability of successful trades
  latencyMin: number; // minimum latency in ms
  latencyMax: number; // maximum latency in ms
  slippageMin: number; // minimum price slippage percentage
  slippageMax: number; // maximum price slippage percentage
  rejectReasons: string[]; // possible rejection reasons
}

export interface SandboxOrderRequest {
  userId: string;
  orderType: 'BUY' | 'SELL';
  instrumentSymbol: string;
  instrumentName: string;
  price: number;
  quantity: number;
  correlationId: string;
}

export class SandboxTradingService {
  private static instance: SandboxTradingService;
  private config: SandboxConfig;
  private orderHistory: Map<string, ProcessedOrderData> = new Map();

  private constructor() {
    this.config = {
      successRate: 0.95, // 95% success rate
      latencyMin: 500,   // 0.5 seconds
      latencyMax: 3000,  // 3 seconds
      slippageMin: -0.1, // -0.1% to +0.1% slippage
      slippageMax: 0.1,
      rejectReasons: [
        'Insufficient funds',
        'Market closed',
        'Price limit exceeded',
        'Instrument not tradeable',
        'System maintenance',
        'Risk management rejection'
      ]
    };
  }

  static getInstance(): SandboxTradingService {
    if (!SandboxTradingService.instance) {
      SandboxTradingService.instance = new SandboxTradingService();
    }
    return SandboxTradingService.instance;
  }

  /**
   * Update sandbox configuration
   */
  updateConfig(newConfig: Partial<SandboxConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current sandbox configuration
   */
  getConfig(): SandboxConfig {
    return { ...this.config };
  }

  /**
   * Simulate order placement
   */
  async placeOrder(orderRequest: SandboxOrderRequest): Promise<ProcessedOrderData> {
    // Track start time for potential latency calculation
    Date.now();
    
    // Log the sandbox order attempt
    tradingLogger.logSystem(
      orderRequest.userId,
      `Sandbox order placement: ${orderRequest.orderType} ${orderRequest.instrumentSymbol}`,
      'INFO',
      {
        orderType: orderRequest.orderType,
        instrumentSymbol: orderRequest.instrumentSymbol,
        price: orderRequest.price,
        quantity: orderRequest.quantity,
        correlationId: orderRequest.correlationId
      }
    );

    // Simulate processing latency
    const latency = this.randomBetween(this.config.latencyMin, this.config.latencyMax);
    await this.sleep(latency);

    // Determine if order should succeed or fail
    const isSuccessful = Math.random() < this.config.successRate;
    
    let orderResult: ProcessedOrderData;

    if (isSuccessful) {
      // Calculate slippage
      const slippagePercent = this.randomBetween(this.config.slippageMin, this.config.slippageMax);
      const executionPrice = orderRequest.price * (1 + slippagePercent / 100);
      
      orderResult = {
        orderId: this.generateSandboxOrderId(),
        status: 'TRADED',
        type: orderRequest.orderType,
        symbol: orderRequest.instrumentSymbol,
        quantity: orderRequest.quantity,
        price: orderRequest.price,
        filledQuantity: orderRequest.quantity,
        orderTime: new Date(),
        isSuccessful: true
      };

      // Add execution details
      const extendedOrderResult = orderResult as ProcessedOrderData & {
        executionPrice: number;
        executionTime: Date;
        slippage: number;
        latency: number;
      };
      extendedOrderResult.executionPrice = executionPrice;
      extendedOrderResult.executionTime = new Date();
      extendedOrderResult.slippage = slippagePercent;
      extendedOrderResult.latency = latency;

      tradingLogger.logSystem(
        orderRequest.userId,
        `Sandbox order executed successfully: ${orderResult.orderId}`,
        'INFO',
        {
          orderId: orderResult.orderId,
          executionPrice,
          slippage: slippagePercent,
          latency
        }
      );
    } else {
      // Generate rejection
      const rejectionReason = this.config.rejectReasons[
        Math.floor(Math.random() * this.config.rejectReasons.length)
      ];

      orderResult = {
        orderId: this.generateSandboxOrderId(),
        status: 'REJECTED',
        type: orderRequest.orderType,
        symbol: orderRequest.instrumentSymbol,
        quantity: orderRequest.quantity,
        price: orderRequest.price,
        filledQuantity: 0,
        orderTime: new Date(),
        isSuccessful: false,
        errorMessage: `Sandbox rejection: ${rejectionReason}`
      };

      tradingLogger.logSystem(
        orderRequest.userId,
        `Sandbox order rejected: ${rejectionReason}`,
        'WARN',
        {
          orderId: orderResult.orderId,
          rejectionReason,
          latency
        }
      );
    }

    // Store in order history
    this.orderHistory.set(orderResult.orderId, orderResult);

    return orderResult;
  }

  /**
   * Get order status (simulated)
   */
  async getOrderStatus(orderId: string): Promise<ProcessedOrderData | null> {
    // Simulate API latency
    await this.sleep(this.randomBetween(100, 500));
    
    return this.orderHistory.get(orderId) || null;
  }

  /**
   * Cancel order (simulated)
   */
  async cancelOrder(orderId: string, userId: string): Promise<boolean> {
    // Simulate API latency
    await this.sleep(this.randomBetween(200, 800));
    
    const order = this.orderHistory.get(orderId);
    if (!order) {
      return false;
    }

    // Only allow cancellation of pending orders
    if (order.status === 'PENDING' || order.status === 'OPEN') {
      order.status = 'CANCELLED';
      this.orderHistory.set(orderId, order);
      
      tradingLogger.logSystem(
        userId,
        `Sandbox order cancelled: ${orderId}`,
        'INFO',
        { orderId }
      );
      
      return true;
    }

    return false;
  }

  /**
   * Get all orders for user (simulated)
   */
  async getUserOrders(): Promise<ProcessedOrderData[]> {
    // Simulate API latency
    await this.sleep(this.randomBetween(300, 1000));
    
    // In a real implementation, this would filter by userId
    // For sandbox, we return all orders (in practice, each user would have their own sandbox instance)
    return Array.from(this.orderHistory.values());
  }

  /**
   * Calculate simulated P&L for a completed trade
   */
  calculatePnL(order: ProcessedOrderData, currentPrice: number): number {
    if (order.status !== 'TRADED' || !order.isSuccessful) {
      return 0;
    }

    const extendedOrder = order as ProcessedOrderData & { executionPrice?: number };
    const executionPrice = extendedOrder.executionPrice || order.price;
    const quantity = order.filledQuantity;

    if (order.type === 'BUY') {
      // For buy orders, profit when current price > execution price
      return (currentPrice - executionPrice) * quantity;
    } else {
      // For sell orders, profit when execution price > current price
      return (executionPrice - currentPrice) * quantity;
    }
  }

  /**
   * Get sandbox trading statistics
   */
  getSandboxStats(): {
    totalOrders: number;
    successfulOrders: number;
    rejectedOrders: number;
    successRate: number;
    averageLatency: number;
    totalVolume: number;
  } {
    const orders = Array.from(this.orderHistory.values());
    const successfulOrders = orders.filter(o => o.isSuccessful);
    const rejectedOrders = orders.filter(o => !o.isSuccessful);
    
    const totalLatency = orders.reduce((sum, order) => {
      const extendedOrder = order as ProcessedOrderData & { latency?: number };
      return sum + (extendedOrder.latency || 0);
    }, 0);
    
    const totalVolume = orders.reduce((sum, order) => {
      return sum + (order.price * order.quantity);
    }, 0);

    return {
      totalOrders: orders.length,
      successfulOrders: successfulOrders.length,
      rejectedOrders: rejectedOrders.length,
      successRate: orders.length > 0 ? successfulOrders.length / orders.length : 0,
      averageLatency: orders.length > 0 ? totalLatency / orders.length : 0,
      totalVolume
    };
  }

  /**
   * Reset sandbox state
   */
  reset(): void {
    this.orderHistory.clear();
  }

  /**
   * Generate sandbox order ID
   */
  private generateSandboxOrderId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `SANDBOX_${timestamp}_${random}`;
  }

  /**
   * Generate random number between min and max
   */
  private randomBetween(min: number, max: number): number {
    return Math.random() * (max - min) + min;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const sandboxTradingService = SandboxTradingService.getInstance();
