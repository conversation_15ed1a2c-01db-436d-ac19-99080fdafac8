/**
 * Signal History Component
 * Displays recent trading signals with execution status
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TrendingUp, TrendingDown, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { TradingSignal } from '@/lib/strategies/strategy-service';

interface SignalHistoryProps {
  signals: TradingSignal[];
  loading?: boolean;
}

export function SignalHistory({ signals, loading = false }: SignalHistoryProps) {
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatPrice = (price: number) => {
    return `₹${price.toFixed(2)}`;
  };

  const getSignalIcon = (type: 'BUY' | 'SELL') => {
    return type === 'BUY' ? (
      <TrendingUp className="h-4 w-4 text-green-400" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-400" />
    );
  };

  const getExecutionStatus = (signal: TradingSignal) => {
    if (signal.isExecuted) {
      return (
        <Badge variant="default" className="text-xs bg-green-600">
          <CheckCircle className="h-3 w-3 mr-1" />
          Executed
        </Badge>
      );
    } else if (signal.executionAttempted) {
      return (
        <Badge variant="destructive" className="text-xs">
          <XCircle className="h-3 w-3 mr-1" />
          Failed
        </Badge>
      );
    } else if (signal.autoTradeEnabled) {
      return (
        <Badge variant="secondary" className="text-xs">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="text-xs">
          <AlertCircle className="h-3 w-3 mr-1" />
          Manual
        </Badge>
      );
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Signal History</CardTitle>
          <CardDescription>Loading recent trading signals...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center justify-between p-3 bg-gray-800 rounded">
                  <div className="flex items-center space-x-3">
                    <div className="h-4 w-4 bg-gray-700 rounded"></div>
                    <div className="space-y-1">
                      <div className="h-4 w-20 bg-gray-700 rounded"></div>
                      <div className="h-3 w-16 bg-gray-700 rounded"></div>
                    </div>
                  </div>
                  <div className="h-6 w-16 bg-gray-700 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Signal History</span>
          <Badge variant="outline" className="text-xs">
            {signals.length} signals
          </Badge>
        </CardTitle>
        <CardDescription>
          Recent trading signals generated by the EMA Scalper strategy
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px]">
          {signals.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No signals generated yet</p>
              <p className="text-xs mt-1">Signals will appear here when the strategy detects trading opportunities</p>
            </div>
          ) : (
            <div className="space-y-3">
              {signals.map((signal, index) => (
                <div
                  key={signal.id || index}
                  className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    {getSignalIcon(signal.signalType)}
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">
                          {signal.signalType} {signal.instrumentSymbol}
                        </span>
                        <Badge 
                          variant={signal.tradingMode === 'LIVE' ? 'destructive' : 'secondary'}
                          className="text-xs"
                        >
                          {signal.tradingMode}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-xs text-gray-400">
                        <span>{formatPrice(signal.signalPrice)}</span>
                        {signal.createdAt && (
                          <span>{formatTime(signal.createdAt)}</span>
                        )}
                        {signal.emaValue && (
                          <span>EMA: {signal.emaValue.toFixed(2)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    {getExecutionStatus(signal)}
                    {signal.last8hHigh && signal.last8lLow && (
                      <div className="text-xs text-gray-500">
                        H: {signal.last8hHigh.toFixed(2)} L: {signal.last8lLow.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
