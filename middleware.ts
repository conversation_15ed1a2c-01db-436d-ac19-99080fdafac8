import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  // Protected routes that require authentication
  const protectedRoutes = ['/dashboard']

  // Auth routes that should redirect if already authenticated
  const authRoutes = ['/', '/sign-up', '/forgot-password']

  // Routes that should be excluded from middleware
  const excludedRoutes = ['/auth/callback', '/reset-password']

  const isProtectedRoute = protectedRoutes.some(route =>
    req.nextUrl.pathname.startsWith(route)
  )

  const isAuthRoute = authRoutes.includes(req.nextUrl.pathname)

  const isExcludedRoute = excludedRoutes.some(route =>
    req.nextUrl.pathname.startsWith(route)
  )

  // Skip middleware for excluded routes
  if (isExcludedRoute) {
    return NextResponse.next()
  }

  // Get the session token from cookies - check multiple possible cookie names
  const sessionToken = req.cookies.get('sb-njcufuxisahkpauhlupp-auth-token') ||
                       req.cookies.get('sb-njcufuxisahkpauhlupp-auth-token-code-verifier') ||
                       req.cookies.get('supabase-auth-token')

  // If accessing a protected route without a session token, redirect to sign in
  if (isProtectedRoute && !sessionToken) {
    const redirectUrl = new URL('/', req.url)
    redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If accessing auth routes with a session token, redirect to dashboard
  if (isAuthRoute && sessionToken) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
