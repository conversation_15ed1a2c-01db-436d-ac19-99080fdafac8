/**
 * Profile Error Component
 * Displays error states for profile data fetching
 */

import { AlertTriangle, RefreshC<PERSON>, Wifi, Key } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ProfileErrorProps {
  error: string;
  onRetry: () => void;
  isRetrying?: boolean;
}

export function ProfileError({ error, onRetry, isRetrying = false }: ProfileErrorProps) {
  const getErrorIcon = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return <Wifi className="h-8 w-8 text-red-400" />;
    }
    if (error.toLowerCase().includes('credential') || error.toLowerCase().includes('token')) {
      return <Key className="h-8 w-8 text-red-400" />;
    }
    return <AlertTriangle className="h-8 w-8 text-red-400" />;
  };

  const getErrorTitle = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return 'Network Error';
    }
    if (error.toLowerCase().includes('credential') || error.toLowerCase().includes('token')) {
      return 'Authentication Error';
    }
    return 'Error Loading Profile';
  };

  const getErrorDescription = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return 'Unable to connect to Dhan API. Please check your internet connection and try again.';
    }
    if (error.toLowerCase().includes('credential') || error.toLowerCase().includes('token')) {
      return 'There was an issue with your API credentials. Please check your configuration.';
    }
    return 'An unexpected error occurred while loading your profile data.';
  };

  return (
    <div className="bg-red-900/20 border border-red-800 rounded-lg p-8 text-center space-y-4">
      <div className="flex justify-center">
        {getErrorIcon()}
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-red-400">
          {getErrorTitle()}
        </h3>
        <p className="text-gray-300 max-w-md mx-auto">
          {getErrorDescription()}
        </p>
        <p className="text-sm text-gray-400 font-mono bg-gray-900/50 px-3 py-2 rounded border max-w-md mx-auto">
          {error}
        </p>
      </div>

      <Button
        onClick={onRetry}
        disabled={isRetrying}
        className="bg-red-600 hover:bg-red-700 text-white"
      >
        {isRetrying ? (
          <>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Retrying...
          </>
        ) : (
          <>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </>
        )}
      </Button>
    </div>
  );
}
