/**
 * EMA Scalper Integration Tests
 * Tests the core functionality of the EMA Scalper strategy
 */

import { EmaScalper, Candle } from '@/lib/strategies/emaScalper';

describe('EMA Scalper Strategy', () => {
  let strategy: EmaScalper;

  beforeEach(() => {
    strategy = new EmaScalper(5); // Use shorter period for testing
  });

  describe('EMA Calculation', () => {
    it('should calculate EMA correctly for a series of values', () => {
      const values = [10, 12, 14, 16, 18, 20];
      const emaValues = strategy['calcEMA'](values); // Access private method for testing
      
      expect(emaValues).toHaveLength(values.length);
      expect(emaValues[0]).toBe(10); // First value should be the same
      expect(emaValues[emaValues.length - 1]).toBeGreaterThan(values[0]); // Should trend upward
    });

    it('should handle empty array', () => {
      const emaValues = strategy['calcEMA']([]);
      expect(emaValues).toEqual([]);
    });

    it('should handle single value', () => {
      const emaValues = strategy['calcEMA']([100]);
      expect(emaValues).toEqual([100]);
    });
  });

  describe('Signal Generation', () => {
    it('should generate BUY signal when price crosses above EMA', () => {
      // Create candles that show an upward trend crossing EMA
      const candles: Candle[] = [
        { close: 100 },
        { close: 98 },
        { close: 96 },
        { close: 94 },
        { close: 92 },
        { close: 95 }, // Start recovery
        { close: 98 },
        { close: 102 }, // Cross above EMA
        { close: 105 }
      ];

      const signals = strategy.runStrategy(candles);
      
      // Should have at least one BUY signal
      const buySignals = signals.filter(s => s.type === 'BUY');
      expect(buySignals.length).toBeGreaterThan(0);
    });

    it('should generate SELL signal when price crosses below EMA', () => {
      // Create candles that show a downward trend crossing EMA
      const candles: Candle[] = [
        { close: 100 },
        { close: 102 },
        { close: 104 },
        { close: 106 },
        { close: 108 },
        { close: 105 }, // Start decline
        { close: 102 },
        { close: 98 }, // Cross below EMA
        { close: 95 }
      ];

      const signals = strategy.runStrategy(candles);
      
      // Should have at least one SELL signal
      const sellSignals = signals.filter(s => s.type === 'SELL');
      expect(sellSignals.length).toBeGreaterThan(0);
    });

    it('should not generate signals with insufficient data', () => {
      const candles: Candle[] = [
        { close: 100 },
        { close: 102 }
      ];

      const signals = strategy.runStrategy(candles);
      expect(signals).toEqual([]);
    });

    it('should include correct signal information', () => {
      const candles: Candle[] = [
        { close: 100 },
        { close: 98 },
        { close: 96 },
        { close: 94 },
        { close: 92 },
        { close: 95 },
        { close: 98 },
        { close: 102 },
        { close: 105 }
      ];

      const signals = strategy.runStrategy(candles);
      
      if (signals.length > 0) {
        const signal = signals[0];
        expect(signal).toHaveProperty('type');
        expect(signal).toHaveProperty('price');
        expect(signal).toHaveProperty('index');
        expect(['BUY', 'SELL']).toContain(signal.type);
        expect(typeof signal.price).toBe('number');
        expect(typeof signal.index).toBe('number');
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle flat price movement', () => {
      const candles: Candle[] = Array(10).fill({ close: 100 });
      const signals = strategy.runStrategy(candles);
      
      // Flat movement should not generate signals
      expect(signals).toEqual([]);
    });

    it('should handle volatile but non-trending movement', () => {
      const candles: Candle[] = [
        { close: 100 },
        { close: 105 },
        { close: 95 },
        { close: 110 },
        { close: 90 },
        { close: 105 },
        { close: 95 },
        { close: 100 }
      ];

      const signals = strategy.runStrategy(candles);
      
      // Volatile but non-trending movement might generate some signals
      // but they should be valid
      signals.forEach(signal => {
        expect(['BUY', 'SELL']).toContain(signal.type);
        expect(signal.price).toBeGreaterThan(0);
        expect(signal.index).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('Performance', () => {
    it('should handle large datasets efficiently', () => {
      // Generate 1000 candles with random walk
      const candles: Candle[] = [];
      let price = 100;
      
      for (let i = 0; i < 1000; i++) {
        price += (Math.random() - 0.5) * 2; // Random walk
        candles.push({ close: Math.max(1, price) }); // Ensure positive price
      }

      const startTime = Date.now();
      const signals = strategy.runStrategy(candles);
      const endTime = Date.now();

      // Should complete within reasonable time (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);
      
      // Should return valid signals
      signals.forEach(signal => {
        expect(['BUY', 'SELL']).toContain(signal.type);
        expect(signal.price).toBeGreaterThan(0);
        expect(signal.index).toBeGreaterThanOrEqual(0);
        expect(signal.index).toBeLessThan(candles.length);
      });
    });
  });

  describe('Strategy Parameters', () => {
    it('should work with different EMA lengths', () => {
      const candles: Candle[] = [
        { close: 100 }, { close: 98 }, { close: 96 }, { close: 94 }, { close: 92 },
        { close: 90 }, { close: 88 }, { close: 86 }, { close: 84 }, { close: 82 },
        { close: 85 }, { close: 88 }, { close: 91 }, { close: 94 }, { close: 97 },
        { close: 100 }, { close: 103 }, { close: 106 }, { close: 109 }, { close: 112 }
      ];

      // Test with different EMA lengths
      [5, 10, 20].forEach(length => {
        const testStrategy = new EmaScalper(length);
        const signals = testStrategy.runStrategy(candles);
        
        // Should generate some signals regardless of length
        // (though the exact signals may differ)
        signals.forEach(signal => {
          expect(['BUY', 'SELL']).toContain(signal.type);
          expect(signal.price).toBeGreaterThan(0);
        });
      });
    });
  });
});
