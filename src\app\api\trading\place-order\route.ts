/**
 * Trading Order Placement API Route
 * Handles BUY/SELL order placement with proper validation, rate limiting, and idempotency
 */

import { NextRequest, NextResponse } from 'next/server';
import { createDhanApiService } from '@/lib/dhan-api';
import { supabase } from '@/lib/supabase';
import { tradingConfigService, TradingConfiguration } from '@/lib/trading-config';
import { sandboxTradingService } from '@/lib/sandbox-trading';
import { tradingLogger } from '@/lib/trading-logger';
import { DhanOrderRequest, ProcessedOrderData } from '@/types/dhan';

interface PlaceOrderRequest {
  userId: string;
  signalId?: string;
  orderType: 'BUY' | 'SELL';
  instrumentSymbol: string;
  instrumentName: string;
  instrumentSecurityId: string;
  instrumentExchange: string;
  price: number;
  quantity?: number;
  correlationId: string;
  tradingMode: 'SANDBOX' | 'LIVE';
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let userId = '';

  try {
    const body: PlaceOrderRequest = await request.json();
    userId = body.userId;
    
    // Validate required fields
    if (!body.userId || !body.orderType || !body.instrumentSymbol || !body.correlationId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user can place more trades
    const canTrade = await tradingConfigService.canPlaceTrade(body.userId);
    if (!canTrade) {
      return NextResponse.json(
        { error: 'Daily trade limit reached or auto-trade disabled' },
        { status: 403 }
      );
    }

    // Check for duplicate correlation ID
    const { data: existingTrade } = await supabase
      .from('executed_trades')
      .select('id')
      .eq('correlation_id', body.correlationId)
      .single();

    if (existingTrade) {
      return NextResponse.json(
        { error: 'Order already processed (duplicate correlation ID)' },
        { status: 409 }
      );
    }

    // Get trading configuration
    const config = await tradingConfigService.getTradingConfig(body.userId);
    if (!config) {
      return NextResponse.json(
        { error: 'Trading configuration not found' },
        { status: 404 }
      );
    }

    let orderResult;
    
    if (body.tradingMode === 'SANDBOX') {
      // Simulate order placement for sandbox mode
      orderResult = await simulateOrderPlacement(body);
    } else {
      // Place actual order through Dhan API
      orderResult = await placeRealOrder(body, config);
    }

    // Store trade execution in database
    const tradeRecord = await storeTradeExecution(body, orderResult);

    // Update trading statistics
    await tradingConfigService.incrementTradeCount(body.userId, 0); // PnL will be calculated later

    // Update signal as executed if signalId provided
    if (body.signalId) {
      await supabase
        .from('trading_signals')
        .update({ 
          is_executed: orderResult.isSuccessful,
          execution_attempted: true 
        })
        .eq('id', body.signalId);
    }

    return NextResponse.json({
      success: true,
      data: {
        tradeId: tradeRecord.id,
        orderId: orderResult.orderId,
        status: orderResult.status,
        tradingMode: body.tradingMode,
        isSuccessful: orderResult.isSuccessful,
        message: orderResult.isSuccessful 
          ? `${body.orderType} order placed successfully` 
          : `Order failed: ${orderResult.errorMessage}`
      }
    });

    // Log successful API call
    const responseTime = Date.now() - startTime;
    tradingLogger.logApiCall(userId, '/api/trading/place-order', 'POST', 200, responseTime);

  } catch (error) {
    console.error('Error in place-order API:', error);

    // Log failed API call
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    tradingLogger.logApiCall(userId, '/api/trading/place-order', 'POST', 500, responseTime, errorMessage);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Simulate order placement for sandbox mode
 */
async function simulateOrderPlacement(orderRequest: PlaceOrderRequest): Promise<ProcessedOrderData> {
  // Use the enhanced sandbox trading service
  return await sandboxTradingService.placeOrder({
    userId: orderRequest.userId,
    orderType: orderRequest.orderType,
    instrumentSymbol: orderRequest.instrumentSymbol,
    instrumentName: orderRequest.instrumentName,
    price: orderRequest.price,
    quantity: orderRequest.quantity || 1,
    correlationId: orderRequest.correlationId
  });
}

/**
 * Place real order through Dhan API
 */
async function placeRealOrder(orderRequest: PlaceOrderRequest, config: TradingConfiguration): Promise<ProcessedOrderData> {
  const apiService = createDhanApiService();
  
  if (!apiService) {
    throw new Error('Dhan API service not available');
  }

  // Build Dhan order request
  const dhanOrderRequest: DhanOrderRequest = {
    dhanClientId: process.env.DHAN_CLIENT_ID!,
    correlationId: orderRequest.correlationId,
    transactionType: orderRequest.orderType,
    exchangeSegment: orderRequest.instrumentExchange,
    productType: 'INTRADAY', // Default to intraday for scalping
    orderType: 'MARKET', // Market orders for immediate execution
    validity: 'DAY',
    securityId: orderRequest.instrumentSecurityId,
    quantity: orderRequest.quantity || config.strategyParams.defaultQuantity || 1,
    afterMarketOrder: false
  };

  const response = await apiService.placeOrder(dhanOrderRequest);

  if (!response.success || !response.data) {
    return {
      orderId: '',
      status: 'REJECTED',
      type: orderRequest.orderType,
      symbol: orderRequest.instrumentSymbol,
      quantity: orderRequest.quantity || 1,
      price: orderRequest.price,
      filledQuantity: 0,
      orderTime: new Date(),
      isSuccessful: false,
      errorMessage: response.error?.errorMessage || 'Order placement failed'
    };
  }

  const orderData = response.data;
  
  return {
    orderId: orderData.orderId,
    status: orderData.orderStatus as ProcessedOrderData['status'],
    type: orderData.transactionType,
    symbol: orderData.tradingSymbol,
    quantity: orderData.quantity,
    price: orderData.price,
    filledQuantity: orderData.filled,
    orderTime: new Date(orderData.exchangeTime),
    isSuccessful: orderData.orderStatus === 'TRADED' || orderData.orderStatus === 'OPEN',
    errorMessage: orderData.omsErrorDescription
  };
}

/**
 * Store trade execution in database
 */
async function storeTradeExecution(
  orderRequest: PlaceOrderRequest,
  orderResult: ProcessedOrderData
): Promise<{ id: string; [key: string]: unknown }> {
  const { data, error } = await supabase
    .from('executed_trades')
    .insert({
      user_id: orderRequest.userId,
      signal_id: orderRequest.signalId || null,
      correlation_id: orderRequest.correlationId,
      order_type: orderRequest.orderType,
      instrument_symbol: orderRequest.instrumentSymbol,
      instrument_name: orderRequest.instrumentName,
      instrument_security_id: orderRequest.instrumentSecurityId,
      instrument_exchange: orderRequest.instrumentExchange,
      order_price: orderRequest.price,
      quantity: orderRequest.quantity || 1,
      order_status: orderResult.status,
      dhan_order_id: orderResult.orderId,
      execution_price: orderResult.price,
      execution_quantity: orderResult.filledQuantity,
      execution_time: orderResult.orderTime,
      trading_mode: orderRequest.tradingMode,
      dhan_response: orderResult,
      error_message: orderResult.errorMessage
    })
    .select()
    .single();

  if (error) {
    console.error('Error storing trade execution:', error);
    throw new Error('Failed to store trade execution');
  }

  return data;
}

// Handle CORS if needed
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
