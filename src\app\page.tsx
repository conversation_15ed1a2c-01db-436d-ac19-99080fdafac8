import { Header } from "@/components/layout/header"
import { SignInForm } from "@/components/auth/sign-in-form"
import { News } from "@/components/layout/news"

export default function Home() {
  return (
    <div className="h-screen bg-background overflow-hidden">
      <Header />

      <main className="flex h-[calc(100vh-3.5rem)]">
        {/* Left side - Sign in form */}
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            <SignInForm />
          </div>
        </div>

        {/* Right side - Testimonial */}
        <div className="relative hidden w-0 flex-1 lg:block">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
            <div className="absolute inset-0 bg-gradient-to-br from-green-900/20 via-transparent to-blue-900/20" />
            <News />
          </div>
        </div>
      </main>
    </div>
  );
}
