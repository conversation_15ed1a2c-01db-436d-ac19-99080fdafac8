"use client"

import { motion } from "framer-motion"
import { Quote } from "lucide-react"

export function Testimonial() {
  return (
    <div className="hidden lg:flex lg:flex-col lg:justify-center lg:px-8">
      <motion.div
        className="mx-auto max-w-md"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Quote className="h-8 w-8 text-green-400" />
          </motion.div>

          <motion.blockquote
            className="text-xl font-medium text-white leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            Wait. Is it so easy to write queries for @supabase ? It&apos;s like simple SQL stuff!
          </motion.blockquote>

          <motion.div
            className="flex items-center space-x-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            <motion.div
              className="h-10 w-10 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <span className="text-white font-semibold text-sm">TB</span>
            </motion.div>
            <div>
              <div className="text-sm font-medium text-white">T0ny_Boy</div>
              <div className="text-xs text-gray-400">@T0ny_Boy</div>
            </div>
          </motion.div>
        </div>

        <motion.div
          className="mt-12 space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.1 }}
        >
          <div className="flex space-x-1">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="h-2 w-2 rounded-full bg-green-400 opacity-60"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{
                  duration: 0.3,
                  delay: 1.3 + i * 0.1,
                  type: "spring",
                  stiffness: 300
                }}
              />
            ))}
          </div>
          <p className="text-xs text-gray-500">
            Join thousands of developers building with Supabase
          </p>
        </motion.div>
      </motion.div>
    </div>
  )
}
