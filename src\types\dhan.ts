/**
 * Dhan API Types and Interfaces
 */

// Dhan API Profile Response
export interface DhanProfileResponse {
  dhanClientId: string;
  tokenValidity: string;
  activeSegment: string;
  ddpi: 'Active' | 'Deactive';
  mtf: 'Active' | 'Deactive';
  dataPlan: 'Active' | 'Deactive';
  dataValidity: string;
}

// Dhan API Error Response
export interface DhanErrorResponse {
  errorType: string;
  errorCode: string;
  errorMessage: string;
}

// Processed Profile Data for UI
export interface ProcessedProfileData {
  clientId: string;
  tokenExpiry: Date;
  tokenExpiryFormatted: string;
  isTokenExpired: boolean;
  segments: string[];
  ddpiStatus: 'Active' | 'Inactive';
  mtfStatus: 'Active' | 'Inactive';
  dataPlanStatus: 'Active' | 'Inactive';
  dataExpiry: Date | null;
  dataExpiryFormatted: string | null;
  isDataExpired: boolean;
}

// API Response wrapper
export interface DhanApiResponse<T> {
  success: boolean;
  data?: T;
  error?: DhanErrorResponse;
}

// Hook state interface
export interface UseDhanProfileState {
  data: ProcessedProfileData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Market Data Types
export interface DhanMarketDataResponse {
  data: {
    [exchange: string]: {
      [securityId: string]: {
        last_price: number;
        ohlc?: {
          open: number;
          close: number;
          high: number;
          low: number;
        };
        net_change?: number;
        volume?: number;
        average_price?: number;
        upper_circuit_limit?: number;
        lower_circuit_limit?: number;
        last_trade_time?: string;
      };
    };
  };
  status: string;
}

// Processed Market Data for UI
export interface ProcessedMarketData {
  symbol: string;
  name: string;
  currentPrice: number;
  change: number;
  changePercent: number;
  isPositive: boolean;
  open?: number;
  high?: number;
  low?: number;
  close?: number;
  volume?: number;
  lastUpdated: Date;
  formattedPrice: string;
  formattedChange: string;
  formattedChangePercent: string;
}

// Market Data Request
export interface MarketDataRequest {
  [exchange: string]: number[];
}

// Instrument Configuration
export interface InstrumentConfig {
  symbol: string;
  name: string;
  exchange: string;
  securityId: number;
}

// Market Data Hook State
export interface UseMarketDataState {
  data: ProcessedMarketData[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refetch: () => Promise<void>;
}

// Order Types
export interface DhanOrderRequest {
  dhanClientId: string;
  correlationId: string; // For idempotency
  transactionType: 'BUY' | 'SELL';
  exchangeSegment: string;
  productType: 'INTRADAY' | 'CNC' | 'MTF';
  orderType: 'MARKET' | 'LIMIT' | 'STOP_LOSS' | 'STOP_LOSS_MARKET';
  validity: 'DAY' | 'IOC';
  securityId: string;
  quantity: number;
  disclosedQuantity?: number;
  price?: number;
  triggerPrice?: number;
  afterMarketOrder?: boolean;
  boProfitValue?: number;
  boStopLossValue?: number;
}

export interface DhanOrderResponse {
  orderId: string;
  orderStatus: 'PENDING' | 'OPEN' | 'CANCELLED' | 'TRADED' | 'REJECTED';
  transactionType: 'BUY' | 'SELL';
  exchangeSegment: string;
  productType: string;
  orderType: string;
  validity: string;
  tradingSymbol: string;
  securityId: string;
  quantity: number;
  disclosedQuantity: number;
  price: number;
  triggerPrice: number;
  afterMarketOrder: boolean;
  drvExpiryDate?: string;
  drvOptionType?: string;
  drvStrikePrice?: number;
  omsErrorCode?: string;
  omsErrorDescription?: string;
  filled: number;
  exchangeTime: string;
  dhanClientId: string;
  algoId?: string;
  correlationId: string;
}

export interface ProcessedOrderData {
  orderId: string;
  status: 'PENDING' | 'OPEN' | 'CANCELLED' | 'TRADED' | 'REJECTED';
  type: 'BUY' | 'SELL';
  symbol: string;
  quantity: number;
  price: number;
  filledQuantity: number;
  orderTime: Date;
  isSuccessful: boolean;
  errorMessage?: string;
}
