# Supabase Setup Guide

This guide will help you set up Supabase for your authentication system.

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign in with GitHub (recommended)
4. Click "New Project"
5. Choose your organization
6. Enter project details:
   - **Name**: `supabase-auth-clone` (or your preferred name)
   - **Database Password**: Generate a strong password
   - **Region**: Choose the closest region to your users
7. Click "Create new project"

## Step 2: Get Your Project Credentials

1. Wait for your project to be created (usually takes 1-2 minutes)
2. Go to **Settings > API** in your project dashboard
3. Copy the following values:
   - **Project URL** (under "Project URL")
   - **Anon key** (under "Project API keys")

## Step 3: Configure Environment Variables

1. In your project root, copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   ```

## Step 4: Configure Authentication Settings

1. Go to **Authentication > Settings** in your Supabase dashboard
2. Configure the following:

### Site URL
- **Site URL**: `http://localhost:3000` (for development)
- For production, use your actual domain: `https://yourdomain.com`

### Redirect URLs
Add these redirect URLs (one per line):
```
http://localhost:3000/auth/callback
http://localhost:3000/dashboard
https://yourdomain.com/auth/callback (for production)
https://yourdomain.com/dashboard (for production)
```

## Step 5: Enable Email Authentication

1. Go to **Authentication > Providers**
2. **Email** should be enabled by default
3. Configure email settings:
   - **Enable email confirmations**: ON (recommended)
   - **Enable email change confirmations**: ON (recommended)
   - **Enable secure email change**: ON (recommended)

## Step 6: Configure Social Providers (Optional)

### GitHub Provider

1. Go to **Authentication > Providers**
2. Find **GitHub** and click the toggle to enable it
3. You'll need to create a GitHub OAuth App:

#### Create GitHub OAuth App
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Click "New OAuth App"
3. Fill in the details:
   - **Application name**: `Supabase Auth Clone`
   - **Homepage URL**: `http://localhost:3000`
   - **Authorization callback URL**: `https://your-project-id.supabase.co/auth/v1/callback`
4. Click "Register application"
5. Copy the **Client ID** and generate a **Client Secret**

#### Configure in Supabase
1. Back in Supabase, paste the GitHub credentials:
   - **Client ID**: Your GitHub OAuth App Client ID
   - **Client Secret**: Your GitHub OAuth App Client Secret
2. Click "Save"

## Step 7: Test Your Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Open [http://localhost:3000](http://localhost:3000)

3. Test the following:
   - [ ] Sign up with email/password
   - [ ] Check your email for confirmation
   - [ ] Sign in with email/password
   - [ ] Test forgot password flow
   - [ ] Test GitHub login (if configured)

## Step 8: Database Setup (Optional)

If you want to store additional user data:

1. Go to **SQL Editor** in your Supabase dashboard
2. Create a profiles table:

```sql
-- Create profiles table
create table profiles (
  id uuid references auth.users on delete cascade not null primary key,
  updated_at timestamp with time zone,
  username text unique,
  full_name text,
  avatar_url text,
  website text,

  constraint username_length check (char_length(username) >= 3)
);

-- Set up Row Level Security (RLS)
alter table profiles enable row level security;

create policy "Public profiles are viewable by everyone." on profiles
  for select using (true);

create policy "Users can insert their own profile." on profiles
  for insert with check (auth.uid() = id);

create policy "Users can update own profile." on profiles
  for update using (auth.uid() = id);

-- Set up automatic profile creation
create function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, full_name, avatar_url)
  values (new.id, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  return new;
end;
$$ language plpgsql security definer;

create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();
```

## Troubleshooting

### Common Issues

1. **"Invalid login credentials"**
   - Check if email confirmation is required
   - Verify the user exists in Authentication > Users

2. **"Invalid redirect URL"**
   - Ensure redirect URLs are properly configured
   - Check for typos in the URLs

3. **GitHub login not working**
   - Verify GitHub OAuth app callback URL
   - Check GitHub client ID and secret in Supabase

4. **Environment variables not loading**
   - Restart your development server after changing `.env.local`
   - Ensure variables start with `NEXT_PUBLIC_`

### Getting Help

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [GitHub Issues](https://github.com/your-username/supabase-auth-clone/issues)

## Production Deployment

When deploying to production:

1. Update environment variables in your hosting platform
2. Update Site URL and Redirect URLs in Supabase
3. Configure custom SMTP for emails (optional)
4. Set up proper error monitoring
5. Enable database backups

---

**You're all set!** 🎉 Your Supabase authentication system is ready to use.
