/**
 * Market Hours Detection Service
 * Handles NSE and MCX market operating hours, holidays, and status detection
 */

export interface MarketHours {
  open: string; // HH:MM format
  close: string; // HH:MM format
  timezone: string;
}

export interface MarketStatus {
  isOpen: boolean;
  status: 'OPEN' | 'CLOSED' | 'PRE_MARKET' | 'AFTER_HOURS' | 'HOLIDAY' | 'WEEKEND';
  nextOpenTime?: Date;
  nextCloseTime?: Date;
  timeUntilOpen?: number; // milliseconds
  timeUntilClose?: number; // milliseconds
}

export interface MarketConfig {
  name: string;
  exchange: string;
  hours: MarketHours;
  preMarketStart?: string;
  afterHoursEnd?: string;
}

// Market configurations
export const MARKET_CONFIGS: Record<string, MarketConfig> = {
  NSE: {
    name: 'NSE Equity',
    exchange: 'NSE',
    hours: {
      open: '09:15',
      close: '15:30',
      timezone: 'Asia/Kolkata'
    },
    preMarketStart: '09:00',
    afterHoursEnd: '16:00'
  },
  MCX: {
    name: 'MCX Commodity',
    exchange: 'MCX',
    hours: {
      open: '09:00',
      close: '23:30',
      timezone: 'Asia/Kolkata'
    },
    preMarketStart: '08:45',
    afterHoursEnd: '23:45'
  }
};

// Indian market holidays for 2025 (this should be updated annually)
const MARKET_HOLIDAYS_2025: string[] = [
  '2025-01-26', // Republic Day
  '2025-03-14', // Holi
  '2025-03-31', // Ram Navami
  '2025-04-14', // Mahavir Jayanti
  '2025-04-18', // Good Friday
  '2025-05-01', // Maharashtra Day
  '2025-08-15', // Independence Day
  '2025-08-16', // Parsi New Year
  '2025-09-07', // Ganesh Chaturthi
  '2025-10-02', // Gandhi Jayanti
  '2025-10-21', // Dussehra
  '2025-11-01', // Diwali Laxmi Puja
  '2025-11-04', // Diwali Balipratipada
  '2025-11-05', // Bhai Dooj
  '2025-12-25', // Christmas
];

/**
 * Get current time in IST
 */
export function getCurrentISTTime(): Date {
  return new Date(new Date().toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));
}

/**
 * Check if a date is a market holiday
 */
export function isMarketHoliday(date: Date): boolean {
  const dateStr = date.toISOString().split('T')[0];
  return MARKET_HOLIDAYS_2025.includes(dateStr);
}

/**
 * Check if a date is a weekend (Saturday or Sunday)
 */
export function isWeekend(date: Date): boolean {
  const day = date.getDay();
  return day === 0 || day === 6; // Sunday = 0, Saturday = 6
}

/**
 * Parse time string (HH:MM) and create Date object for today
 */
function parseTimeToday(timeStr: string, baseDate: Date = getCurrentISTTime()): Date {
  const [hours, minutes] = timeStr.split(':').map(Number);
  const date = new Date(baseDate);
  date.setHours(hours, minutes, 0, 0);
  return date;
}

/**
 * Get next trading day (skipping weekends and holidays)
 */
export function getNextTradingDay(fromDate: Date = getCurrentISTTime()): Date {
  const nextDay = new Date(fromDate);
  nextDay.setDate(nextDay.getDate() + 1);
  
  while (isWeekend(nextDay) || isMarketHoliday(nextDay)) {
    nextDay.setDate(nextDay.getDate() + 1);
  }
  
  return nextDay;
}

/**
 * Get market status for a specific exchange
 */
export function getMarketStatus(exchange: 'NSE' | 'MCX'): MarketStatus {
  const config = MARKET_CONFIGS[exchange];
  const now = getCurrentISTTime();
  
  // Check if it's a weekend or holiday
  if (isWeekend(now) || isMarketHoliday(now)) {
    const nextTradingDay = getNextTradingDay(now);
    const nextOpenTime = parseTimeToday(config.hours.open, nextTradingDay);
    
    return {
      isOpen: false,
      status: isWeekend(now) ? 'WEEKEND' : 'HOLIDAY',
      nextOpenTime,
      timeUntilOpen: nextOpenTime.getTime() - now.getTime()
    };
  }
  
  // Parse market hours for today
  const marketOpen = parseTimeToday(config.hours.open, now);
  const marketClose = parseTimeToday(config.hours.close, now);
  const preMarketStart = config.preMarketStart ? parseTimeToday(config.preMarketStart, now) : null;
  const afterHoursEnd = config.afterHoursEnd ? parseTimeToday(config.afterHoursEnd, now) : null;
  
  const currentTime = now.getTime();
  const openTime = marketOpen.getTime();
  const closeTime = marketClose.getTime();
  
  // Determine market status
  if (currentTime >= openTime && currentTime <= closeTime) {
    return {
      isOpen: true,
      status: 'OPEN',
      nextCloseTime: marketClose,
      timeUntilClose: closeTime - currentTime
    };
  } else if (preMarketStart && currentTime >= preMarketStart.getTime() && currentTime < openTime) {
    return {
      isOpen: false,
      status: 'PRE_MARKET',
      nextOpenTime: marketOpen,
      timeUntilOpen: openTime - currentTime
    };
  } else if (afterHoursEnd && currentTime > closeTime && currentTime <= afterHoursEnd.getTime()) {
    return {
      isOpen: false,
      status: 'AFTER_HOURS',
      nextOpenTime: parseTimeToday(config.hours.open, getNextTradingDay(now)),
      timeUntilOpen: parseTimeToday(config.hours.open, getNextTradingDay(now)).getTime() - currentTime
    };
  } else {
    // Market is closed
    const nextTradingDay = currentTime > closeTime ? getNextTradingDay(now) : now;
    const nextOpenTime = parseTimeToday(config.hours.open, nextTradingDay);
    
    return {
      isOpen: false,
      status: 'CLOSED',
      nextOpenTime,
      timeUntilOpen: nextOpenTime.getTime() - currentTime
    };
  }
}

/**
 * Get combined market status for both NSE and MCX
 */
export function getCombinedMarketStatus(): {
  nse: MarketStatus;
  mcx: MarketStatus;
  anyOpen: boolean;
  allClosed: boolean;
} {
  const nse = getMarketStatus('NSE');
  const mcx = getMarketStatus('MCX');
  
  return {
    nse,
    mcx,
    anyOpen: nse.isOpen || mcx.isOpen,
    allClosed: !nse.isOpen && !mcx.isOpen
  };
}

/**
 * Format time until next event (open/close)
 */
export function formatTimeUntil(milliseconds: number): string {
  if (milliseconds <= 0) return 'Now';
  
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Get market status display text
 */
export function getMarketStatusText(status: MarketStatus): string {
  switch (status.status) {
    case 'OPEN':
      return 'Market Open';
    case 'CLOSED':
      return 'Market Closed';
    case 'PRE_MARKET':
      return 'Pre-Market';
    case 'AFTER_HOURS':
      return 'After Hours';
    case 'HOLIDAY':
      return 'Market Holiday';
    case 'WEEKEND':
      return 'Weekend';
    default:
      return 'Unknown';
  }
}
