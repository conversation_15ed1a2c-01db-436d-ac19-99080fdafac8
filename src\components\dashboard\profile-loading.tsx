/**
 * Profile Loading Component
 * Displays loading skeleton for profile data
 */

export function ProfileLoading() {
  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 space-y-6 animate-pulse">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="h-6 w-6 bg-gray-700 rounded"></div>
        <div className="h-6 w-48 bg-gray-700 rounded"></div>
      </div>

      {/* Client ID and Token Validity */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 bg-gray-700 rounded"></div>
            <div className="h-4 w-20 bg-gray-700 rounded"></div>
          </div>
          <div className="h-12 bg-gray-700 rounded"></div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 bg-gray-700 rounded"></div>
            <div className="h-4 w-24 bg-gray-700 rounded"></div>
          </div>
          <div className="h-6 w-40 bg-gray-700 rounded"></div>
        </div>
      </div>

      {/* Active Segments */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <div className="h-4 w-4 bg-gray-700 rounded"></div>
          <div className="h-4 w-28 bg-gray-700 rounded"></div>
        </div>
        <div className="flex flex-wrap gap-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-8 w-20 bg-gray-700 rounded-full"></div>
          ))}
        </div>
      </div>

      {/* Account Status */}
      <div className="space-y-3">
        <div className="h-4 w-24 bg-gray-700 rounded"></div>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-900/50 border border-gray-700 rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <div className="h-4 w-12 bg-gray-700 rounded"></div>
                <div className="h-6 w-16 bg-gray-700 rounded-full"></div>
              </div>
              <div className="h-3 w-full bg-gray-700 rounded"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Data Plan Validity */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <div className="h-4 w-4 bg-gray-700 rounded"></div>
          <div className="h-4 w-32 bg-gray-700 rounded"></div>
        </div>
        <div className="h-4 w-36 bg-gray-700 rounded"></div>
      </div>
    </div>
  );
}
