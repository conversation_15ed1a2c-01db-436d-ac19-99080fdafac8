/**
 * Market Data Loading Component
 * Displays loading skeleton for market data cards
 */

export function MarketDataLoading() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[1, 2].map((i) => (
        <div key={i} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 space-y-4 animate-pulse">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 w-24 bg-gray-700 rounded"></div>
              <div className="h-4 w-16 bg-gray-700 rounded"></div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 bg-gray-700 rounded"></div>
              <div className="h-3 w-8 bg-gray-700 rounded"></div>
            </div>
          </div>

          {/* Price Information */}
          <div className="space-y-3">
            <div className="flex items-baseline space-x-3">
              <div className="h-8 w-32 bg-gray-700 rounded"></div>
              <div className="h-6 w-24 bg-gray-700 rounded-full"></div>
            </div>

            {/* OHLC Data */}
            <div className="grid grid-cols-3 gap-4 pt-3 border-t border-gray-700">
              {[1, 2, 3].map((j) => (
                <div key={j} className="text-center space-y-1">
                  <div className="h-3 w-8 bg-gray-700 rounded mx-auto"></div>
                  <div className="h-4 w-12 bg-gray-700 rounded mx-auto"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Last Updated */}
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-700">
            <div className="h-3 w-3 bg-gray-700 rounded"></div>
            <div className="h-3 w-32 bg-gray-700 rounded"></div>
          </div>
        </div>
      ))}
    </div>
  );
}
