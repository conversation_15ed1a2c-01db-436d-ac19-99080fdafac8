{"name": "trade-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "setup": "echo 'Please follow the setup guide in scripts/setup-supabase.md' && echo 'Copy .env.local.example to .env.local and add your Supabase credentials'", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-switch": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.57.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "tailwindcss": "^4", "ts-jest": "^29.4.1", "typescript": "^5"}}