/**
 * Historical Data Storage Service
 * Manages localStorage-based caching for last available market data
 */

import { ProcessedMarketData } from '@/types/dhan';

export interface HistoricalMarketData extends ProcessedMarketData {
  capturedAt: string; // ISO timestamp when data was captured
  marketSession: string; // e.g., "2025-01-04-REGULAR" or "2025-01-04-CLOSING"
  dataSource: 'LIVE' | 'API' | 'WEBSOCKET';
}

export interface MarketDataCache {
  lastUpdate: string; // ISO timestamp
  data: HistoricalMarketData[];
  version: string; // Cache version for migration purposes
}

const STORAGE_KEY = 'market_data_cache';
const CACHE_VERSION = '1.0.0';
const MAX_CACHE_AGE_DAYS = 7; // Keep data for 7 days max

/**
 * Check if localStorage is available
 */
function isLocalStorageAvailable(): boolean {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get current market session identifier
 */
function getCurrentMarketSession(): string {
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  const hour = now.getHours();
  
  // Determine session type based on time
  if (hour >= 9 && hour < 16) {
    return `${dateStr}-REGULAR`;
  } else if (hour >= 16 && hour < 24) {
    return `${dateStr}-CLOSING`;
  } else {
    return `${dateStr}-PREMARKET`;
  }
}

/**
 * Load market data cache from localStorage
 */
export function loadMarketDataCache(): MarketDataCache | null {
  if (!isLocalStorageAvailable()) {
    console.warn('localStorage not available, historical data caching disabled');
    return null;
  }

  try {
    const cached = localStorage.getItem(STORAGE_KEY);
    if (!cached) return null;

    const cache: MarketDataCache = JSON.parse(cached);
    
    // Check cache version
    if (cache.version !== CACHE_VERSION) {
      console.log('Cache version mismatch, clearing old cache');
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    // Check cache age
    const cacheAge = Date.now() - new Date(cache.lastUpdate).getTime();
    const maxAge = MAX_CACHE_AGE_DAYS * 24 * 60 * 60 * 1000;
    
    if (cacheAge > maxAge) {
      console.log('Cache expired, clearing old data');
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    return cache;
  } catch (error) {
    console.error('Error loading market data cache:', error);
    localStorage.removeItem(STORAGE_KEY);
    return null;
  }
}

/**
 * Save market data to cache
 */
export function saveMarketDataCache(data: ProcessedMarketData[], dataSource: 'LIVE' | 'API' | 'WEBSOCKET' = 'API'): boolean {
  if (!isLocalStorageAvailable()) {
    return false;
  }

  try {
    const now = new Date().toISOString();
    const session = getCurrentMarketSession();

    const historicalData: HistoricalMarketData[] = data.map(item => ({
      ...item,
      capturedAt: now,
      marketSession: session,
      dataSource
    }));

    const cache: MarketDataCache = {
      lastUpdate: now,
      data: historicalData,
      version: CACHE_VERSION
    };

    localStorage.setItem(STORAGE_KEY, JSON.stringify(cache));
    console.log(`Market data cached: ${data.length} instruments at ${now}`);
    return true;
  } catch (error) {
    console.error('Error saving market data cache:', error);
    return false;
  }
}

/**
 * Get last available market data
 */
export function getLastAvailableData(): HistoricalMarketData[] | null {
  const cache = loadMarketDataCache();
  return cache?.data || null;
}

/**
 * Get specific instrument's last available data
 */
export function getInstrumentLastData(symbol: string): HistoricalMarketData | null {
  const cache = loadMarketDataCache();
  if (!cache) return null;

  return cache.data.find(item => item.symbol === symbol) || null;
}

/**
 * Check if cached data is from today
 */
export function isCacheFromToday(): boolean {
  const cache = loadMarketDataCache();
  if (!cache) return false;

  const cacheDate = new Date(cache.lastUpdate).toDateString();
  const today = new Date().toDateString();
  
  return cacheDate === today;
}

/**
 * Check if cached data is recent (within specified hours)
 */
export function isCacheRecent(maxHours: number = 24): boolean {
  const cache = loadMarketDataCache();
  if (!cache) return false;

  const cacheAge = Date.now() - new Date(cache.lastUpdate).getTime();
  const maxAge = maxHours * 60 * 60 * 1000;
  
  return cacheAge <= maxAge;
}

/**
 * Get cache metadata
 */
export function getCacheMetadata(): {
  hasCache: boolean;
  lastUpdate: string | null;
  dataCount: number;
  isFromToday: boolean;
  isRecent: boolean;
  ageInHours: number;
} {
  const cache = loadMarketDataCache();
  
  if (!cache) {
    return {
      hasCache: false,
      lastUpdate: null,
      dataCount: 0,
      isFromToday: false,
      isRecent: false,
      ageInHours: 0
    };
  }

  const ageMs = Date.now() - new Date(cache.lastUpdate).getTime();
  const ageInHours = ageMs / (1000 * 60 * 60);

  return {
    hasCache: true,
    lastUpdate: cache.lastUpdate,
    dataCount: cache.data.length,
    isFromToday: isCacheFromToday(),
    isRecent: isCacheRecent(),
    ageInHours
  };
}

/**
 * Clear market data cache
 */
export function clearMarketDataCache(): boolean {
  if (!isLocalStorageAvailable()) {
    return false;
  }

  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log('Market data cache cleared');
    return true;
  } catch (error) {
    console.error('Error clearing market data cache:', error);
    return false;
  }
}

/**
 * Update specific instrument data in cache
 */
export function updateInstrumentInCache(updatedData: ProcessedMarketData, dataSource: 'LIVE' | 'API' | 'WEBSOCKET' = 'WEBSOCKET'): boolean {
  const cache = loadMarketDataCache();
  if (!cache) return false;

  try {
    const now = new Date().toISOString();
    const session = getCurrentMarketSession();

    // Find and update the instrument
    const instrumentIndex = cache.data.findIndex(item => item.symbol === updatedData.symbol);
    
    if (instrumentIndex >= 0) {
      // Update existing instrument
      cache.data[instrumentIndex] = {
        ...updatedData,
        capturedAt: now,
        marketSession: session,
        dataSource
      };
    } else {
      // Add new instrument
      cache.data.push({
        ...updatedData,
        capturedAt: now,
        marketSession: session,
        dataSource
      });
    }

    cache.lastUpdate = now;
    localStorage.setItem(STORAGE_KEY, JSON.stringify(cache));
    return true;
  } catch (error) {
    console.error('Error updating instrument in cache:', error);
    return false;
  }
}

/**
 * Get data freshness indicator
 */
export function getDataFreshness(data: HistoricalMarketData): {
  freshness: 'FRESH' | 'RECENT' | 'STALE' | 'OLD';
  ageInMinutes: number;
  description: string;
} {
  const ageMs = Date.now() - new Date(data.capturedAt).getTime();
  const ageInMinutes = ageMs / (1000 * 60);

  if (ageInMinutes < 5) {
    return {
      freshness: 'FRESH',
      ageInMinutes,
      description: 'Very recent data'
    };
  } else if (ageInMinutes < 60) {
    return {
      freshness: 'RECENT',
      ageInMinutes,
      description: 'Recent data'
    };
  } else if (ageInMinutes < 24 * 60) {
    return {
      freshness: 'STALE',
      ageInMinutes,
      description: 'Data from today'
    };
  } else {
    return {
      freshness: 'OLD',
      ageInMinutes,
      description: 'Older data'
    };
  }
}
