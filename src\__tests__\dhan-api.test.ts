/**
 * Tests for Dhan API integration
 */

import { DhanApiService } from '@/lib/dhan-api';
import { DhanProfileResponse } from '@/types/dhan';

// Mock fetch for testing
global.fetch = jest.fn();

describe('DhanApiService', () => {
  const mockClientId = '1000237632';
  const mockAccessToken = 'mock-token';
  let apiService: DhanApiService;

  beforeEach(() => {
    apiService = new DhanApiService(mockClientId, mockAccessToken);
    jest.clearAllMocks();
  });

  describe('processProfileData', () => {
    it('should correctly process profile data', () => {
      const mockRawData: DhanProfileResponse = {
        dhanClientId: '1000237632',
        tokenValidity: '30/03/2025 15:37',
        activeSegment: 'Equity, Derivative, Currency, Commodity',
        ddpi: 'Active',
        mtf: 'Active',
        dataPlan: 'Active',
        dataValidity: '2024-12-05 09:37:52.0',
      };

      const processed = DhanApiService.processProfileData(mockRawData);

      expect(processed.clientId).toBe('1000237632');
      expect(processed.segments).toEqual(['Equity', 'Derivative', 'Currency', 'Commodity']);
      expect(processed.ddpiStatus).toBe('Active');
      expect(processed.mtfStatus).toBe('Active');
      expect(processed.dataPlanStatus).toBe('Active');
      expect(processed.tokenExpiryFormatted).toContain('2025');
      expect(processed.dataExpiryFormatted).toContain('2024');
    });

    it('should handle expired tokens correctly', () => {
      const mockRawData: DhanProfileResponse = {
        dhanClientId: '1000237632',
        tokenValidity: '01/01/2020 15:37', // Past date
        activeSegment: 'Equity',
        ddpi: 'Active',
        mtf: 'Active',
        dataPlan: 'Active',
        dataValidity: '2020-01-01 09:37:52.0', // Past date
      };

      const processed = DhanApiService.processProfileData(mockRawData);

      expect(processed.isTokenExpired).toBe(true);
      expect(processed.isDataExpired).toBe(true);
    });

    it('should handle inactive statuses correctly', () => {
      const mockRawData: DhanProfileResponse = {
        dhanClientId: '1000237632',
        tokenValidity: '30/03/2025 15:37',
        activeSegment: 'Equity',
        ddpi: 'Deactive',
        mtf: 'Deactive',
        dataPlan: 'Deactive',
        dataValidity: '2024-12-05 09:37:52.0',
      };

      const processed = DhanApiService.processProfileData(mockRawData);

      expect(processed.ddpiStatus).toBe('Inactive');
      expect(processed.mtfStatus).toBe('Inactive');
      expect(processed.dataPlanStatus).toBe('Inactive');
    });
  });

  describe('API error handling', () => {
    it('should handle 401 authentication errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({
          errorType: 'Invalid_Authentication',
          errorCode: 'DH-901',
          errorMessage: 'Client ID or user generated access token is invalid or expired.',
        }),
      });

      const result = await apiService.getProfile();

      expect(result.success).toBe(false);
      expect(result.error?.errorType).toBe('AUTHENTICATION_ERROR');
      expect(result.error?.errorCode).toBe('INVALID_TOKEN');
    });

    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new TypeError('Network error'));

      const result = await apiService.getProfile();

      expect(result.success).toBe(false);
      expect(result.error?.errorType).toBe('NETWORK_ERROR');
      expect(result.error?.errorMessage).toContain('Network error');
    });

    it('should handle rate limit errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => ({}),
      });

      const result = await apiService.getProfile();

      expect(result.success).toBe(false);
      expect(result.error?.errorType).toBe('RATE_LIMIT_ERROR');
      expect(result.error?.errorCode).toBe('TOO_MANY_REQUESTS');
    });
  });
});
