/**
 * Profile Card Component
 * Displays user profile information from Dhan API
 */

import { ProcessedProfileData } from '@/types/dhan';
import { User, Clock, Shield, TrendingUp, Database, AlertTriangle } from 'lucide-react';

interface ProfileCardProps {
  profile: ProcessedProfileData;
}

export function ProfileCard({ profile }: ProfileCardProps) {
  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 space-y-6">
      <div className="flex items-center space-x-3">
        <User className="h-6 w-6 text-blue-400" />
        <h2 className="text-xl font-semibold text-white">Profile Information</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Client ID */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Client ID</span>
          </div>
          <p className="text-lg font-mono text-white bg-gray-900/50 px-3 py-2 rounded border">
            {profile.clientId}
          </p>
        </div>

        {/* Token Validity */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Token Validity</span>
          </div>
          <div className="flex items-center space-x-2">
            <p className={`text-lg font-medium ${profile.isTokenExpired ? 'text-red-400' : 'text-green-400'}`}>
              {profile.tokenExpiryFormatted}
            </p>
            {profile.isTokenExpired && (
              <AlertTriangle className="h-4 w-4 text-red-400" />
            )}
          </div>
          {profile.isTokenExpired && (
            <p className="text-sm text-red-400">Token has expired</p>
          )}
        </div>
      </div>

      {/* Active Segments */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium text-gray-300">Active Segments</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {profile.segments.map((segment, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-blue-900/30 border border-blue-800 text-blue-300 text-sm rounded-full"
            >
              {segment}
            </span>
          ))}
        </div>
      </div>

      {/* Account Status */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-300">Account Status</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <StatusItem
            label="DDPI"
            status={profile.ddpiStatus}
            description="Demat Debit and Pledge Instruction"
          />
          <StatusItem
            label="MTF"
            status={profile.mtfStatus}
            description="Margin Trading Facility"
          />
          <StatusItem
            label="Data Plan"
            status={profile.dataPlanStatus}
            description="Market Data Subscription"
          />
        </div>
      </div>

      {/* Data Plan Validity */}
      {profile.dataExpiry && (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Database className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">Data Plan Validity</span>
          </div>
          <div className="flex items-center space-x-2">
            <p className={`text-sm ${profile.isDataExpired ? 'text-red-400' : 'text-green-400'}`}>
              {profile.dataExpiryFormatted}
            </p>
            {profile.isDataExpired && (
              <AlertTriangle className="h-4 w-4 text-red-400" />
            )}
          </div>
          {profile.isDataExpired && (
            <p className="text-sm text-red-400">Data plan has expired</p>
          )}
        </div>
      )}
    </div>
  );
}

interface StatusItemProps {
  label: string;
  status: 'Active' | 'Inactive';
  description: string;
}

function StatusItem({ label, status, description }: StatusItemProps) {
  const isActive = status === 'Active';
  
  return (
    <div className="bg-gray-900/50 border border-gray-700 rounded-lg p-3">
      <div className="flex items-center justify-between mb-1">
        <span className="text-sm font-medium text-white">{label}</span>
        <span
          className={`px-2 py-1 text-xs rounded-full ${
            isActive
              ? 'bg-green-900/30 border border-green-800 text-green-300'
              : 'bg-red-900/30 border border-red-800 text-red-300'
          }`}
        >
          {status}
        </span>
      </div>
      <p className="text-xs text-gray-400">{description}</p>
    </div>
  );
}
