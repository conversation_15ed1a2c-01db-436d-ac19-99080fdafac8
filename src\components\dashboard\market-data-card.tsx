/**
 * Market Data Card Component
 * Displays live market data for individual instruments
 */

import { ProcessedMarketData } from '@/types/dhan';
import { TrendingUp, TrendingDown, Activity, Clock, Wifi, Database, AlertCircle } from 'lucide-react';
import { DataSource } from '@/hooks/use-market-data';
import { HistoricalMarketData } from '@/lib/historical-data-storage';

interface MarketDataCardProps {
  data: ProcessedMarketData | HistoricalMarketData;
  dataSource?: DataSource;
  isLive?: boolean;
}

export function MarketDataCard({ data, dataSource = 'DEMO' }: MarketDataCardProps) {
  const {
    symbol,
    name,
    isPositive,
    open,
    high,
    low,
    formattedPrice,
    formattedChange,
    formattedChangePercent,
    lastUpdated,
  } = data;

  const trendColor = isPositive ? 'text-green-400' : 'text-red-400';
  const bgColor = isPositive ? 'bg-green-900/20 border-green-800' : 'bg-red-900/20 border-red-800';
  const TrendIcon = isPositive ? TrendingUp : TrendingDown;

  const getDataSourceIcon = () => {
    switch (dataSource) {
      case 'WEBSOCKET':
        return <Wifi className="h-4 w-4 text-green-400" />;
      case 'LIVE':
        return <Activity className="h-4 w-4 text-blue-400" />;
      case 'HISTORICAL':
        return <Database className="h-4 w-4 text-orange-400" />;
      case 'DEMO':
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getDataSourceLabel = () => {
    switch (dataSource) {
      case 'WEBSOCKET':
        return 'Live WebSocket';
      case 'LIVE':
        return 'Live API';
      case 'HISTORICAL':
        return 'Historical';
      case 'DEMO':
        return 'Demo';
    }
  };

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white">{name}</h3>
          <p className="text-sm text-gray-400 font-mono">{symbol}</p>
        </div>
        <div className="flex items-center space-x-2">
          {getDataSourceIcon()}
          <span className="text-xs text-gray-400">{getDataSourceLabel()}</span>
        </div>
      </div>

      {/* Price Information */}
      <div className="space-y-3">
        <div className="flex items-baseline space-x-3">
          <span className="text-2xl font-bold text-white">₹{formattedPrice}</span>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full border ${bgColor}`}>
            <TrendIcon className={`h-3 w-3 ${trendColor}`} />
            <span className={`text-sm font-medium ${trendColor}`}>
              {formattedChange}
            </span>
            <span className={`text-sm font-medium ${trendColor}`}>
              ({formattedChangePercent})
            </span>
          </div>
        </div>

        {/* OHLC Data */}
        {(open || high || low) && (
          <div className="grid grid-cols-3 gap-4 pt-3 border-t border-gray-700">
            {open && (
              <div className="text-center">
                <p className="text-xs text-gray-400">Open</p>
                <p className="text-sm font-medium text-white">₹{open.toFixed(2)}</p>
              </div>
            )}
            {high && (
              <div className="text-center">
                <p className="text-xs text-gray-400">High</p>
                <p className="text-sm font-medium text-green-400">₹{high.toFixed(2)}</p>
              </div>
            )}
            {low && (
              <div className="text-center">
                <p className="text-xs text-gray-400">Low</p>
                <p className="text-sm font-medium text-red-400">₹{low.toFixed(2)}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Last Updated */}
      <div className="flex items-center space-x-2 pt-2 border-t border-gray-700">
        <Clock className="h-3 w-3 text-gray-400" />
        <span className="text-xs text-gray-400">
          Updated: {lastUpdated.toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Kolkata',
          })}
        </span>
      </div>
    </div>
  );
}
