/**
 * Market Data Error Component
 * Displays error states for market data fetching
 */

import { AlertTriangle, RefreshCw, Wifi, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MarketDataErrorProps {
  error: string;
  onRetry: () => void;
  isRetrying?: boolean;
}

export function MarketDataError({ error, onRetry, isRetrying = false }: MarketDataErrorProps) {
  const getErrorIcon = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return <Wifi className="h-8 w-8 text-red-400" />;
    }
    return <AlertTriangle className="h-8 w-8 text-red-400" />;
  };

  const getErrorTitle = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return 'Network Error';
    }
    if (error.toLowerCase().includes('credential') || error.toLowerCase().includes('token')) {
      return 'Authentication Error';
    }
    return 'Market Data Unavailable';
  };

  const getErrorDescription = () => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return 'Unable to connect to market data service. Please check your internet connection and try again.';
    }
    if (error.toLowerCase().includes('credential') || error.toLowerCase().includes('token')) {
      return 'There was an issue with API credentials. Market data may be temporarily unavailable.';
    }
    return 'Unable to fetch live market data at the moment. This could be due to market hours or service maintenance.';
  };

  return (
    <div className="bg-red-900/20 border border-red-800 rounded-lg p-8 text-center space-y-4">
      <div className="flex justify-center">
        {getErrorIcon()}
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-red-400">
          {getErrorTitle()}
        </h3>
        <p className="text-gray-300 max-w-md mx-auto">
          {getErrorDescription()}
        </p>
        <p className="text-sm text-gray-400 font-mono bg-gray-900/50 px-3 py-2 rounded border max-w-md mx-auto">
          {error}
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
        <Button
          onClick={onRetry}
          disabled={isRetrying}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          {isRetrying ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Retrying...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </>
          )}
        </Button>
        
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <TrendingUp className="h-4 w-4" />
          <span>Market data will auto-refresh when available</span>
        </div>
      </div>
    </div>
  );
}
