/**
 * Auto-Trade Toggle Component
 * Provides toggle control for enabling/disabling auto-trading
 */

'use client';

import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Play, Square, Settings } from 'lucide-react';
import { TradingConfiguration, TradingStats } from '@/lib/trading-config';

interface AutoTradeToggleProps {
  config: TradingConfiguration | null;
  stats: TradingStats | null;
  isActive: boolean;
  loading: boolean;
  onToggleAutoTrade: () => Promise<void>;
  onSwitchTradingMode: (mode: 'SANDBOX' | 'LIVE') => Promise<void>;
  onStartTrading: () => void;
  onStopTrading: () => void;
}

export function AutoTradeToggle({
  config,
  stats,
  isActive,
  loading,
  onToggleAutoTrade,
  onSwitchTradingMode,
  onStartTrading,
  onStopTrading
}: AutoTradeToggleProps) {
  const [switching, setSwitching] = useState(false);

  const handleToggleAutoTrade = async () => {
    setSwitching(true);
    try {
      await onToggleAutoTrade();
    } finally {
      setSwitching(false);
    }
  };

  const handleSwitchMode = async (mode: 'SANDBOX' | 'LIVE') => {
    setSwitching(true);
    try {
      await onSwitchTradingMode(mode);
    } finally {
      setSwitching(false);
    }
  };

  if (loading || !config) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Auto-Trade Control</span>
          </CardTitle>
          <CardDescription>Loading trading configuration...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-700 rounded"></div>
            <div className="h-10 bg-gray-700 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Auto-Trade Control</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge 
              variant={config.tradingMode === 'LIVE' ? 'destructive' : 'secondary'}
              className="text-xs"
            >
              {config.tradingMode}
            </Badge>
            <Badge 
              variant={isActive ? 'default' : 'outline'}
              className="text-xs"
            >
              {isActive ? 'ACTIVE' : 'INACTIVE'}
            </Badge>
          </div>
        </CardTitle>
        <CardDescription>
          Manage automated trading settings and execution
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Auto-Trade Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <label className="text-sm font-medium">Auto-Trade</label>
            <p className="text-xs text-gray-400">
              Automatically execute trades when signals are generated
            </p>
          </div>
          <Switch
            checked={config.autoTradeEnabled}
            onCheckedChange={handleToggleAutoTrade}
            disabled={switching}
          />
        </div>

        {/* Trading Mode Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Trading Mode</label>
          <div className="flex space-x-2">
            <Button
              variant={config.tradingMode === 'SANDBOX' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSwitchMode('SANDBOX')}
              disabled={switching || config.tradingMode === 'SANDBOX'}
              className="flex-1"
            >
              Sandbox
            </Button>
            <Button
              variant={config.tradingMode === 'LIVE' ? 'destructive' : 'outline'}
              size="sm"
              onClick={() => handleSwitchMode('LIVE')}
              disabled={switching || config.tradingMode === 'LIVE'}
              className="flex-1"
            >
              Live
            </Button>
          </div>
          {config.tradingMode === 'LIVE' && (
            <div className="flex items-center space-x-2 p-2 bg-red-900/20 border border-red-800 rounded text-xs text-red-300">
              <AlertTriangle className="h-4 w-4" />
              <span>Live mode will place real orders with real money</span>
            </div>
          )}
        </div>

        {/* Service Control */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Service Control</label>
          <div className="flex space-x-2">
            <Button
              variant={isActive ? 'outline' : 'default'}
              size="sm"
              onClick={onStartTrading}
              disabled={isActive || !config.autoTradeEnabled}
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-2" />
              Start
            </Button>
            <Button
              variant={isActive ? 'destructive' : 'outline'}
              size="sm"
              onClick={onStopTrading}
              disabled={!isActive}
              className="flex-1"
            >
              <Square className="h-4 w-4 mr-2" />
              Stop
            </Button>
          </div>
        </div>

        {/* Trading Statistics */}
        {stats && (
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-700">
            <div className="text-center">
              <div className="text-lg font-semibold text-white">
                {stats.dailyTradeCount}
              </div>
              <div className="text-xs text-gray-400">Trades Today</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-white">
                {stats.remainingTrades}
              </div>
              <div className="text-xs text-gray-400">Remaining</div>
            </div>
            <div className="text-center col-span-2">
              <div className={`text-lg font-semibold ${stats.dailyPnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₹{stats.dailyPnl.toFixed(2)}
              </div>
              <div className="text-xs text-gray-400">Daily P&L</div>
            </div>
          </div>
        )}

        {/* Status Indicators */}
        <div className="flex items-center justify-between text-xs text-gray-400 pt-2 border-t border-gray-700">
          <span>Strategy: {config.strategyName}</span>
          <span>EMA Length: {config.strategyParams.emaLength}</span>
        </div>
      </CardContent>
    </Card>
  );
}
