import Link from "next/link"
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-4">
      <div className="text-center space-y-6 max-w-md">
        <div className="space-y-2">
          <h1 className="text-6xl font-bold text-green-400">404</h1>
          <h2 className="text-2xl font-semibold text-white">Page not found</h2>
          <p className="text-gray-400">
            Sorry, we couldn&apos;t find the page you&apos;re looking for.
          </p>
        </div>
        
        <Link href="/">
          <Button className="bg-green-600 hover:bg-green-700 text-white">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Sign In
          </Button>
        </Link>
      </div>
    </div>
  )
}
