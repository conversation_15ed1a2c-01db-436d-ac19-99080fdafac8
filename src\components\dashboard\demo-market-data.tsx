/**
 * Demo Market Data Component
 * Shows sample market data when API is not available
 */

import { ProcessedMarketData } from '@/types/dhan';
import { MarketDataCard } from './market-data-card';
import { Info, TrendingUp } from 'lucide-react';

const demoMarketData: ProcessedMarketData[] = [
  {
    symbol: 'NIFTY50',
    name: 'NIFTY 50',
    currentPrice: 24734.30,
    change: 19.25,
    changePercent: 0.08,
    isPositive: true,
    open: 24715.05,
    high: 24756.80,
    low: 24698.15,
    close: 24715.05,
    volume: 0,
    lastUpdated: new Date(),
    formattedPrice: '24,734.30',
    formattedChange: '+19.25',
    formattedChangePercent: '+0.08%',
  },
  {
    symbol: 'SILVER',
    name: 'Silver',
    currentPrice: 91250.00,
    change: -485.00,
    changePercent: -0.53,
    isPositive: false,
    open: 91735.00,
    high: 91890.00,
    low: 91180.00,
    close: 91735.00,
    volume: 0,
    lastUpdated: new Date(),
    formattedPrice: '91,250.00',
    formattedChange: '-485.00',
    formattedChangePercent: '-0.53%',
  },
];

export function DemoMarketData() {
  return (
    <div className="space-y-6">
      {/* Demo Notice */}
      <div className="bg-blue-900/20 border border-blue-800 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-blue-300">Demo Market Data</h3>
            <p className="text-sm text-blue-200">
              Live market data is currently unavailable due to API rate limits or service issues. Showing sample data to demonstrate the dashboard functionality.
            </p>
            <p className="text-xs text-blue-300">
              Market data will automatically refresh when the service becomes available. The system uses intelligent retry with exponential backoff.
            </p>
          </div>
        </div>
      </div>

      {/* Demo Market Data Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {demoMarketData.map((data) => (
          <MarketDataCard key={data.symbol} data={data} />
        ))}
      </div>

      {/* Market Hours Notice */}
      <div className="bg-yellow-900/20 border border-yellow-800 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <TrendingUp className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-yellow-300">Market Information</h3>
            <p className="text-sm text-yellow-200">
              Live market data is available during market hours (9:15 AM - 3:30 PM IST for equity, extended hours for commodities).
            </p>
            <div className="text-xs text-yellow-300 space-y-1">
              <p>• NIFTY 50: NSE Equity Index (9:15 AM - 3:30 PM IST)</p>
              <p>• Silver: MCX Commodity (9:00 AM - 11:30 PM IST)</p>
              <p>• Data refreshes every 30 seconds during market hours</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
